""" Generate and send a test panel member email, substituing the recipient's email address
    Generates a scheduled email document based on a survey panel member's data (as per scheduler agent),
    then sends the email (as per the email sender agent).
"""
import argparse
import datetime
import re
from simple_salesforce import Salesforce, format_soql
from lib.settings import settings
from lib import emailapi, sfapi, sqsapi, portaldbapi, locale as liblocale
from agents.email_scheduler_panel_users import get_contact_from_spm, get_signatory_from_spm, get_customer_survey_from_spm, get_email_service_provider, determine_vertical, sanitise_html

SF_RESOURCE_URL = 'file.force.com'
DEFAULT_SENDER = '<EMAIL>' # TODO: move to secrets


def get_panel_member_from_sf(sf:Salesforce, panel_member_id:str) -> dict:
    print(' >> FETCHING: SURVEY PANEL MEMBER FROM SF...')
    soql = """
    SELECT Id,
            Name,
            SurveyJsId__c,
            Contact__r.Id,
            Contact__r.<PERSON><PERSON>,
            Contact__r.<PERSON>,
            Contact__r.<PERSON>,
            Contact__r.<PERSON>,
            Contact__r.Language__c,
            Contact__r.Location__r.Timezone__c,
            Survey_Email_Triggered__c,
            Survey_Email_Reminder_1__c,
            Survey_Email_Reminder_2__c,
            Survey_Email_Reminder_3__c,
            Survey_Type__c,
            Survey_Client__r.Id,
            Survey_Client__r.Survey_Name__c,
            Survey_Client__r.Signatory__r.Id,
            Survey_Client__r.Signatory__r.Name,
            Survey_Client__r.Signatory__r.Email,
            Survey_Client__r.Signatory__r.Title,
            Survey_Client__r.Signatory__r.Account.Name,
            Survey_Client__r.Signatory__r.Banner__c,
            Survey_Client__r.Signatory__r.Signature__c,
            Survey_Client__r.Customers_Client__c,
            Survey_Client__r.Customer_Survey__r.Id,
            Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId,
            Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Timezone__c,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id,
            Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name,
            Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Send_Emails_Via_SES__c,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id,
            Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Send_Emails_Via_SES__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c,
            Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c
    FROM  Survey_Panel_Member__c
    WHERE Id = {panel_member_id}
    """

    query:str = format_soql(soql, panel_member_id=panel_member_id)

    panel_member = None
    for spm in sfapi.bulk_query(sf, query):
        panel_member = spm

    return panel_member


def process_panel_member(sf:Salesforce, spm:dict, account_record_types, email_template_name:str, new_recipient:str) -> dict:
    # largely a copy of process_participants from the email_scheduler_panel_users agent

    # cs_property_name:str = 'Live_Survey_First_Request__c'
    spm_property_name:str = 'Survey_Email_Triggered__c'
    now:datetime.datetime = datetime.datetime.now(datetime.UTC)
    if email_template_name.endswith('_1'):
        # cs_property_name:str = 'Live_Survey_Second_Request__c'
        spm_property_name:str = 'Survey_Email_Reminder_1__c'
    elif email_template_name.endswith('_2'):
        # cs_property_name:str = 'Live_Survey_Third_Request__c'
        spm_property_name:str = 'Survey_Email_Reminder_2__c'
    elif email_template_name.endswith('_3'):
        # cs_property_name:str = 'Live_Survey_Fourth_Request__c'
        spm_property_name:str = 'Survey_Email_Reminder_3__c'

    contact = get_contact_from_spm(spm)
    signatory = get_signatory_from_spm(spm)
    customer_survey = get_customer_survey_from_spm(spm)
    csr_id:str = spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id')
    survey_type = spm.get('Survey_Type__c')
    # has_been_schduled_key = f'{contact.get("Id")}-{spm.get('SurveyJsId__c')}-{email_template_name}'
    recipient = new_recipient
    holding_group_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name', '')
    vertical = determine_vertical(account_record_types, spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId'))
    external_communication_email = customer_survey.get('External_Communication_Email_Address__c')
    banner_attachment = None
    signature_attachment = None
    banner = signatory.get('Banner__c')
    signature = signatory.get('Signature__c')
    # survey_name_key = f'{contact.get("Id")}-{spm.get('SurveyJsId__c')}'
    is_multi_profile = False  #len(contact_profile_count.get(contact.get('Id'), [])) > 1
    sender = external_communication_email if external_communication_email else DEFAULT_SENDER
    locale = liblocale.get_sf_language_to_locale_formatted(contact.get('Language__c'))
    agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name')

    survey_name = spm.get('Survey_Client__r.Survey_Name__c')
    if not survey_name:
        survey_name = agency_brand_name
    # survey_name = ', '.join(contact_survey_names.get(survey_name_key, []))
    if survey_type == 'Barometer':  # and len(contact_survey_names.get(survey_name_key, [])) > 1:
        # for Barometer if the PM is being surveyed for multiple surveys, the email subject is wanted as
        # "How do you rate working on your accounts?" rather than "How do you rate working on AccountA, AccountB, AccountC?"
        survey_name = 'your accounts'
    from_name = None
    email_service_provider = get_email_service_provider(spm)

    # determine agency brand name based on org-level (and handle accounts that do not support office yet)
    if spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c') == "Level 6": # "Level 6 == With Market"
        agency_brand_name = spm.get('Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name')

    if is_multi_profile:
        agency_brand_name = holding_group_name

    # format survey names and fallback to agency brand name if there's none
    # FIXME: would prefer not to re-use `agency_brand_name` here, but requires a mass template update.
    if survey_name:
        agency_brand_name = survey_name

    if signatory and banner and SF_RESOURCE_URL in banner:
        cid = f'cid:{signatory.get("Id")}-banner'
        banner = re.sub(r'src="[^"]+"', f'src="{cid}"', banner)
        banner_attachment = {
            'disposition': 'inline',
            'key': f'{signatory.get("Id")}-banner.jpg',
            'file_type': 'image/jpeg',
            'content_id': f'{signatory.get("Id")}-banner',
        }

    if signatory and signature and SF_RESOURCE_URL in signature:
        from_name = signatory.get('Name')
        cid = f'cid:{signatory.get("Id")}'
        signature = re.sub(r'src="[^"]+"', f'src="{cid}"', signature)
        signature_attachment = {
            'disposition': 'inline',
            'key': f'{signatory.get("Id")}.jpg',
            'file_type': 'image/jpeg',
            'content_id': signatory.get('Id'),
        }

    # this is a fallback in case the signatory image is not available/missing
    if signatory and not signature:
        signature_name = '' if signatory.get('Name') == None else signatory.get('Name', '')
        signature_title = '' if signatory.get('Title') == None else signatory.get('Title', '')
        signature_account = spm.get('Survey_Client__r.Signatory__r.Account.Name') if spm.get('Survey_Client__r.Signatory__r.Account.Name') else ''
        signature_email = '' if signatory.get('Email') == None else signatory.get('Email', '')
        signature = f'<p><strong>{signature_name}</strong></p><p>{signature_title}</p><p>{signature_account}</p><p>{signature_email}</p>'
        from_name = signatory.get('Name')

    # calculate scheduled send date
    timezone = None
    scheduled_date_local = now
    scheduled_date_utc = now

    # construct template metadata and data
    template_data = {
        'FirstName': sanitise_html(contact.get('FirstName')),
        'LastName': sanitise_html(contact.get('LastName')),
        'SurveyDate': customer_survey.get('Live_Survey_Start_Date__c'),
        'LiveSurveyEndDate': customer_survey.get('Live_Survey_End_Date__c'),
        'SurveyLink': f'{settings.survey_ui_link.rstrip('/')}/survey/{spm.get('SurveyJsId__c')}',
        'Banner': banner,
        'Signatory': signature,
        'AgencyBrand': sanitise_html(agency_brand_name),
        'HoldingGroup': sanitise_html(holding_group_name),
        'OptOutLink': f'{settings.survey_ui_link.rstrip('/')}/optout/{contact.get('Id')}',
        'PrivacyPolicyLink': f'{settings.survey_ui_link.rstrip('/')}/privacy/{spm.get('SurveyJsId__c')}',
    }
    template_metadata = {
        'contact_id': contact.get('Id'),
        'survey_panel_member_id': spm.get('Id'),
        'survey_client_id': spm.get('Survey_Client__r.Id'),
        'customer_survey_id': spm.get('Survey_Client__r.Customer_Survey__r.Id'),
        'customer_survey_round_id': csr_id,
        # this property is used to filter sendgrid email events, see: api/agents/src/handlers/sendgrid/event_handler.py
        # suffix appended in non-prod environments to prevent tracking of test emails by prod
        'tracked_template_name': 'ignore_this_test_email',
    }

    schedule_doc = {
        'type': 'panel',
        'survey_id': spm.get('SurveyJsId__c'),
        'contact_id': contact.get('Id'),
        'survey_panel_member_id': spm.get('Id'),
        'survey_client_id': spm.get('Survey_Client__r.Id'),
        'customer_survey_id': spm.get('Survey_Client__r.Customer_Survey__r.Id'),
        'customer_survey_round_id': csr_id,
        'survey_round_id': spm.get('Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id'),
        'sender_email': sender,
        'sender_name': from_name,
        'recipient': recipient,
        'template_name': email_template_name,
        'template_data': template_data,
        'template_metadata': template_metadata,
        'language': locale,
        'vertical': vertical,
        'banner_attachment': banner_attachment,
        'signature_attachment': signature_attachment,
        'scheduled_date_local': scheduled_date_local.isoformat(),
        'scheduled_date_utc': scheduled_date_utc,
        'scheduled_date_timezone': timezone,
        'sf_spm_field': spm_property_name,
        'last_modified': now,
        'email_service_provider': email_service_provider,
        'test_email': True,
        'survey_panel_member_ids': [spm.get('Id')],
        'survey_client_ids': [spm.get('Survey_Client__r.Id')],
        'customer_survey_ids': [spm.get('Survey_Client__r.Customer_Survey__r.Id')],
    }
    return schedule_doc


def send_email(db, email):
    # code lifted from email_sender_panel_users agent
    sqs:sqsapi.SQS = sqsapi.SQS()
    es:emailapi.EmailService = emailapi.EmailService(db, sqs)

    # id:bson.ObjectId = email.get('_id')
    # spm_ids:list[str] = email.get('survey_panel_member_ids')
    sender_email:str = email.get('sender_email')
    sender_name:str = email.get('sender_name')
    recipient:str = email.get('recipient')
    email_template_name:str = email.get('template_name')
    template_data:dict = email.get('template_data')
    template_metadata:dict = email.get('template_metadata')
    send_at:datetime = email.get('scheduled_date_utc')
    language:str = email.get('language')
    vertical:str = email.get('vertical')
    banner_attachment:str = email.get('banner_attachment')
    signature_attachment:str = email.get('signature_attachment')
    # sf_spm_field:str = email.get('sf_spm_field')
    portaldb_record_map:str = None  #f'scheduledemails-{id}'
    email_service_provider:str = email.get('email_service_provider', 'sendgrid')

    # push email to SQS queue
    try:
        es.send(sender_email, 
                recipient, 
                email_template_name, 
                template_data, 
                template_metadata,
                send_at=send_at,
                from_name=sender_name,
                language=language, 
                vertical=vertical, 
                banner_attachment=banner_attachment,
                signature_attachment=signature_attachment,
                portaldb_record_map=portaldb_record_map,
                service_provider=email_service_provider)

        print(f'   :: Email Scheduled: for {recipient} at {send_at} UTC')
    except Exception as e:
        print(f"Failed to send email for record {id}: {e}")


def main(survey_panel_member_id:str, template_name:str, new_recipient:str, writeit:bool = False) -> None:
    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db:portaldbapi.DocumentDB = portaldbapi.DocumentDB()

    account_record_types:dict[str,dict] = {x.Name: x for x in sfapi.get_all(sf, sfapi.AccountRecordType)}

    spm = get_panel_member_from_sf(sf, survey_panel_member_id)
    if not spm:
        raise ValueError(f'No survey panel member found with ID {survey_panel_member_id}')
    
    email_schedule_doc = process_panel_member(sf, spm, account_record_types, template_name, new_recipient)

    if writeit and email_schedule_doc:
        print(' # Sending email...')
        send_email(db, email_schedule_doc)


if __name__ == '__main__':
    ap = argparse.ArgumentParser()
    ap.add_argument('survey_panel_member_id', default=None, help='single survey panel member to run for')
    ap.add_argument('template_name', type=str, help='The email template name')
    ap.add_argument('new_recipient', type=str, help='The new recipient email address to send the test email to')
    ap.add_argument('--writeit', action='store_true', help='Actually send the email')
    args = ap.parse_args()

    main(args.survey_panel_member_id, args.template_name, args.new_recipient, args.writeit)
