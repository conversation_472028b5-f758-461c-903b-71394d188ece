from transformers import AutoModelForSequenceClassification, AutoTokenizer
import torch
import numpy as np
import json
import datasets
import argparse


def main(test_name, vertical):
    # Load pretrained model and tokenizer
    model_name = f"crc-feedbacksentiment-{vertical}-{test_name}-model"
    model = AutoModelForSequenceClassification.from_pretrained(model_name)
    tokenizer = AutoTokenizer.from_pretrained(model_name)

    with open(f"crc-feedbacksentiment-{test_name}-ds/{vertical}_categories.json", 'r') as f:
        category_by_lm_id = {v['lm_id']: v for v in json.loads(f.read())}

    ds = datasets.load_from_disk(f"crc-feedbacksentiment-{test_name}-ds")
    valid_ds = ds[f'valid_{vertical}']

    for row_idx in range(len(valid_ds)):
        # Tokenize the input text
        row_text = valid_ds['text'][row_idx].strip()
        row_labels = valid_ds['labels'][row_idx]
        inputs = tokenizer(row_text, return_tensors="pt")

        # Perform inference
        with torch.no_grad():
            outputs = model(**inputs)

        def sigmoid(x):
            return 1 / (1 + np.exp(-x))
        probabilities = sigmoid(outputs.logits)
        predictions = (np.array(probabilities) > 0.5).astype(int)

        print(row_text)
        for p, x in enumerate(predictions[0]):
            if x:
                print("ADQMODEL", category_by_lm_id[p]['full_name'], float(probabilities[0][p]))

        for p, x in enumerate(row_labels):
            if x:
                print("CODEIT", category_by_lm_id[p]['full_name'])

        input("Press Enter to continue...")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("test_name")
    ap.add_argument("--vertical", choices=['manufacturing', 'advertising'], default='advertising')
    args = ap.parse_args()

    main(args.test_name, args.vertical)
