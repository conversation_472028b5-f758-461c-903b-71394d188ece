import pymongo
import warnings
import logging
import pymongo.collection
import pymongo.mongo_client
from simple_salesforce import Salesforce
from pydantic import BaseModel, model_validator, Field
from typing import Any, Optional
import datetime
import lib.sfapi as sfapi
from lib.settings import settings
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorDatabase, AsyncIOMotorCollection


PORTALDB_DATABASE: str = "portaldb"
PORTALDB_CONFIRM_ACCOUNTS_COLLECTION: str = "confirmaccounts"
PORTALDB_CONFIRM_PANEL_COLLECTION: str = "confirmpanel"
PORTALDB_PENDING_CUSTOMER_CONTACTS_COLLECTION: str = "pendingcustomercontacts"
PORTALDB_ACCESSLOG_COLLECTION: str = "accesslog"
PORTALDB_SURVEYS_REQUEST_LOG_COLLECTION: str = "surveysrequestlog"
PORTALDB_SURVEY_COLLECTION: str = "surveys"
PORTALDB_CONFIG_COLLECTION: str = "config"
PORTALDB_NAVIGATION_COLLECTION: str = "navigation"
PORTALDB_CUSTOMER_CONTACTS_COLLECTION: str = "customercontacts"
PORTALDB_FEATURES_COLLECTION: str = "features"
PORTALDB_RESPONSES_COLLECTION: str = "responses"
PORTALDB_RESPONSES_BAROMETER_COLLECTION: str = "responsesbarometer"
PORTALDB_SENDGRID_EVENTS_COLLECTION: str = "sendgridevents"
PORTALDB_SES_EVENTS_COLLECTION: str = "sesevents"
PORTALDB_MARKETS_COLLECTION: str = "markets"
PORTALDB_EMAILLOG_COLLECTION: str = "emaillog"
PORTALDB_SCHEDULED_EMAILS_COLLECTION: str = "scheduledemails"
MONGO_BATCH_SIZE = 100

ALL_COLLECTION_INDEXES = {
    'accesslog': [
        {'keys': {'last_access': 1}},
        {'keys': {'contact_id': 1, 'type': 1}},
        {'keys': {'contact_id': 1, 'assumed_role_id': 1, 'type': 1}},
        {'keys': {'type': 1, 'last_access': 1}},
    ],
    'config': [],
    'confirmaccounts': [
        {'keys': {'customer_survey_round_id': 1}},
        {'keys': {'Id': 1}, 'options': {'unique': True}},
        {'keys': {"customer_survey_round_id": 1, "account_managers": 1, "accounts_confirmed_start_date": 1}},
        {'keys': {"Id": 1, "account_managers": 1}},
    ],
    'confirmpanel': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
        {'keys': {"panel_managers": 1}},
        {'keys': {"Id": 1, "panel_managers": 1}},
        {'keys': {"customer_survey_round_id": 1, "panel_managers": 1, "panel_confirmed_start_date": 1}},
        {'keys': {"customer_survey_round_id": 1, "account_managers": 1, "panel_confirmed_start_date": 1}},
    ],
    'customercontacts': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
        {'keys': {"_lastsynced": 1}},
    ],
    'dashboards': [
        {'keys': {"type": 1}},
    ],
    'emaillog': [],
    'features': [
        {'keys': {"contact_id": 1}, 'options': {'unique': True}},
    ],
    'pendingcustomercontacts': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
    ],
    'responses': [
        {'keys': {"Id": 1}, 'options': {'unique': False}},
        {'keys': {"_lastsynced": 1}},
    ],
    'sfcontacts': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
        {'keys': {"Id": 1, "AccountId": 1}},
        {'keys': {"AccountId": 1}},
    ],
    'sfcustomerclientaccounts': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
    ],
    'sfcustomersurvey': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
    ],
    'sfcustomersurveyround': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
    ],
    'sfreports': [],
    'sfsurveyclient': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
    ],
    'surveyrounds': [
        {'keys': {"Id": 1}, 'options': {'unique': True}},
        {'keys': {"_permissions": 1}},
    ],
    'surveys': [],
}

class ClientOrganisationLevel(BaseModel):
    Id: str|None = None
    name: str|None = None


class ClientOrganisationLevels(BaseModel):
    level_1: ClientOrganisationLevel
    level_2: ClientOrganisationLevel
    level_3: ClientOrganisationLevel
    level_4: ClientOrganisationLevel
    level_5: ClientOrganisationLevel
    level_6: ClientOrganisationLevel
    level_7: ClientOrganisationLevel

class ConfirmPanelMember(BaseModel):
    contact_id: str
    contact_name: str
    contact_email: str
    contact_type: str | None = None
    contact_seniority: str | None = None
    contact_language: str | None = None
    contact_job_function: str | None = None
    contact_job_function_2: str | None = None
    contact_job_title: str | None = None
    contact_division: str | None = None
    contact_location: str | None = None
    contact_location_id: str | None = None
    contact_account_site: str | None = None
    contact_account_site_id: str | None = None
    account_id: str | None = None
    serial_non_responder: bool = False
    in_round: bool
    survey_panel_manager_id: str|None = None
    response_status: bool|None = False
    bounced_status: bool|None = False
    survey_name: str|None = None
    selected: Optional[bool] = False
    not_confirmed_resolving_required: Optional[bool] = False


class ConfirmedPanel(BaseModel):
    panel: list[ConfirmPanelMember]
    all_panel: list[ConfirmPanelMember] = [] #TODO: remove this after MIG-02 complete
    confirm_date: datetime.datetime|None = None
    confirm_user_id: str|None = None
    confirm_user_email: str|None = None
    confirm_user_name: str|None = None
    stats: dict[str, list] = {}


class ConfirmPanelPanelManager(BaseModel):
    panel_manager_id: str
    panel_manager_email: str
    panel_manager_name: str


class ClientOrganisationLevel(BaseModel):
    Id: str|None = None
    name: str|None = None


class ClientOrganisationLevels(BaseModel):
    level_1: ClientOrganisationLevel
    level_2: ClientOrganisationLevel
    level_3: ClientOrganisationLevel
    level_4: ClientOrganisationLevel
    level_5: ClientOrganisationLevel
    level_6: ClientOrganisationLevel
    level_7: ClientOrganisationLevel


class ConfirmPanel(BaseModel):
    Id: str
    panel_managers: list[ConfirmPanelPanelManager]
    account_managers: list
    final_account_managers: list[str] = []
    panel: list[ConfirmPanelMember]
    final_confirmed_panel: list[ConfirmPanelMember]|None = []
    client_id: str
    client_name: str
    client_market: str|None = None
    client_market_id: str|None = None
    client_market_language: str|None = None
    client_organisation_level: ClientOrganisationLevels
    account_id: str
    account_name: str
    survey_name: str|None = None
    customer_survey_id: str
    customer_survey_name: str
    customer_survey_round_id: str
    customer_survey_round_name: str|None = None
    team_id: str|None = None
    team_name: str|None = None
    team_market: str|None = None
    confirm_status: bool = False
    confirmed_panel: list[ConfirmedPanel] = []
    confirmed_panel_last_save_all_panel: list[ConfirmPanelMember]|None = None #TODO: change to list after MIG-02 complete
    confirmed_panel_last_save_date: datetime.datetime|None = None
    confirmed_panel_last_save_user_id: str|None = None
    confirmed_panel_last_save_user_name: str|None = None
    confirmed_panel_last_save_user_email: str|None = None
    email_sender: str|None = None
    accounts_confirmed_start_date: datetime.datetime
    accounts_confirmed_by_date: datetime.datetime
    panel_confirmed_start_date: datetime.datetime
    panel_confirmed_by_date: datetime.datetime
    live_survey_start_date: datetime.datetime 
    live_survey_first_request_date: datetime.datetime
    live_survey_second_request_date: datetime.datetime
    live_survey_third_request_date: datetime.datetime
    live_survey_fourth_request_date: datetime.datetime
    live_survey_end_date: datetime.datetime
    sfsync_date: datetime.datetime | None = None


class ConfirmAccountsPanelManager(BaseModel):
    panel_manager_id: str
    panel_manager_email: str
    panel_manager_name: str
    panel_manager_first_name: str | None = None
    panel_manager_last_name: str | None = None


class ConfirmAccountsAccountManager(BaseModel):
    account_manager_id: str
    account_manager_email: str
    account_manager_name: str|None = None


class ConfirmAccountsAccount(BaseModel):
    account_id: str
    account_name: str
    in_round: bool
    selected: Optional[bool] = False
    account_manager_id: str|None = None
    survey_panel_managers: list[ConfirmAccountsPanelManager] = Field(default_factory=list)
    signatory_id: str|None = None
    signatory_email: str|None = None
    signatory_name: str|None = None
    survey_name: str|None = None
    duplicate_account: bool = False
    market_id: str|None = None
    not_confirmed_resolving_required: Optional[bool] = False
    added_by_contact: str|None = None

    @model_validator(mode="before")
    @classmethod
    def move_client_market_id_to_market_id(cls, data):
        if isinstance(data, dict) and "client_market_id" in data and "market_id" not in data:
            data["market_id"] = data["client_market_id"]
        return data


class ConfirmedAccount(BaseModel):
    accounts: list[ConfirmAccountsAccount]
    all_accounts: list[ConfirmAccountsAccount] = []
    confirm_date: datetime.datetime|None = None
    confirm_user_id: str|None = None
    confirm_user_email: str|None = None
    confirm_user_name: str|None = None
    stats: dict[str, list] = {}


class ConfirmAccountsDelegation(BaseModel):
    delegate_email: str
    email_account_delegation_sent: bool
    sent_at: datetime.datetime


class ConfirmAccounts(BaseModel):
    Id: str
    account_managers: list[ConfirmAccountsAccountManager]
    survey_round: str
    customer_survey_round_name: str|None = None
    customer_survey_round_id: str|None = None
    customer_survey_name: str
    accounts: list[ConfirmAccountsAccount]
    confirmed_accounts: list[ConfirmedAccount] = []
    confirmed_accounts_last_save_all_accounts: list[ConfirmAccountsAccount]|None = None #TODO: default to this a list after MIG-01 is complete
    confirmed_accounts_last_save_date: datetime.datetime|None = None
    confirmed_accounts_last_save_user_id: str|None = None
    confirmed_accounts_last_save_user_name: str|None = None
    # final_confirmed_accounts: list[ConfirmedAccount] = []
    delegation: list[dict] = []
    client_id: str
    client_name: str
    client_market: str|None = None
    client_market_id: str|None = None
    client_organisation_level: ClientOrganisationLevels
    team_id: str|None = None
    team_name: str|None = None
    team_market: str|None = None
    confirm_status: bool = False
    auto_confirmed: bool = False
    email_sender: str|None = None
    accounts_confirmed_start_date: datetime.datetime
    accounts_confirmed_by_date: datetime.datetime
    panel_confirmed_start_date: datetime.datetime
    panel_confirmed_by_date: datetime.datetime
    live_survey_start_date: datetime.datetime
    live_survey_first_request_date: datetime.datetime
    live_survey_second_request_date: datetime.datetime
    live_survey_third_request_date: datetime.datetime
    live_survey_fourth_request_date: datetime.datetime
    live_survey_end_date: datetime.datetime
    sfsync_date: datetime.datetime | None = None


class SurveyQuestionPanelMember(BaseModel):
    survey_panel_member_id: str
    survey_panel_member_name: str


class SurveyQuestion(BaseModel):
    panel_members: list[SurveyQuestionPanelMember]
    score_question: str
    score_question_en: str|None = None
    score_question_id: str
    feedback_question: str
    feedback_question_en: str|None = None
    feedback_question_id: str
    is_extra_question: bool


class SurveyConfigPage(BaseModel):
    name: str
    title: str
    elements: list[dict[str, Any]]


class SurveyConfig(BaseModel):
    locale: str|None = None
    pages: list[SurveyConfigPage]
    completeText: str|None = None


class Survey(BaseModel):
    start_date: datetime.datetime
    end_date: datetime.datetime
    client_name: str|None = None
    account_id: str|None = None
    config: SurveyConfig
    questions: list[SurveyQuestion]
    response: dict[str, Any] | None = None
    response_date: datetime.datetime | None = None
    sfsync_date: datetime.datetime | None = None
    deleted: bool = False
    survey_type: str|None = None


class PendingCustomerContact(BaseModel):
    internal_id: str
    first_name: str
    last_name: str
    email: str
    created_date: datetime.datetime
    sfsync_date: datetime.datetime | None = None
    # Id: str | None = None


class SrpReport(BaseModel):
    Id: str
    contact_id: str
    version_id: str
    customer_survey_round_id: str|None = None
    customer_survey_round_title: str|None= None
    s3_filename: str
    title: str
    report_type: str
    published: bool = False
    published_date: datetime.datetime
    last_modified_date: datetime.datetime
    _lastsynced: datetime.datetime


class DocumentDBAPI:
    def __init__(self, portaldb_url):
        self.connection_string: str = portaldb_url
        self.client: pymongo.mongo_client.MongoClient = self._get_portaldb_connection(self.connection_string)
        # collections: apps
        self.config: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].config
        self.navigation: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].navigation
        self.features: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].features
        self.dashboards: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].dashboards
        self.responses: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].responses
        self.customercontacts: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].customercontacts
        self.pendingcustomercontacts: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].pendingcustomercontacts
        self.surveys: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].surveys
        self.reports: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sfreports
        self.confirmaccounts: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].confirmaccounts
        self.confirmpanel: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].confirmpanel
        self.surveyrounds: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].surveyrounds
        self.accesslog: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].accesslog
        self.surveysrequestlog: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].surveysrequestlog
        self.emaillog: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].emaillog
        self.norms: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].norms
        self.markets: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].markets
        self.srpreports: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].srpreports
        self.accounts: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].customerclientaccounts
        # collections: sf mirrors
        self.customersurveyround: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sfcustomersurveyround
        self.customersurveys: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sfcustomersurvey
        self.surveyclients: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sfsurveyclients
        self.contacts: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sfcontacts
        self.customerclientaccounts: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sfcustomerclientaccounts
        # collections: sendgrid and ses
        self.sendgridevents: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sendgridevents
        self.sesevents: pymongo.collection.Collection = self.client[PORTALDB_DATABASE].sesevents

    def _get_portaldb_connection(self, url) -> pymongo.mongo_client.MongoClient:
        # make it stop moaning about documentdb
        oldlevel = logging.getLogger("pymongo.client").level
        try:
            logging.getLogger("pymongo.client").setLevel(logging.CRITICAL)
            with warnings.catch_warnings():
                warnings.simplefilter("ignore")
                return pymongo.mongo_client.MongoClient(url)
        finally:
            logging.getLogger("pymongo.client").setLevel(oldlevel)


class DocumentDBAsyncAPI:
    def __init__(self, portaldb_url):
        self.connection_string: str = portaldb_url
        self.client: AsyncIOMotorDatabase = AsyncIOMotorClient(self.connection_string)
        # collections
        self.dashboards: AsyncIOMotorCollection = self.client[PORTALDB_DATABASE].dashboards
        self.responses: AsyncIOMotorCollection = self.client[PORTALDB_DATABASE].responses
        self.responsesbarometer: AsyncIOMotorCollection = self.client[PORTALDB_DATABASE].responsesbarometer
        self.customercontacts: AsyncIOMotorCollection = self.client[PORTALDB_DATABASE].customercontacts
        self.norms: AsyncIOMotorCollection = self.client[PORTALDB_DATABASE].norms
        self.markets: AsyncIOMotorCollection = self.client[PORTALDB_DATABASE].markets
        self.contacts: AsyncIOMotorCollection = self.client[PORTALDB_DATABASE].sfcontacts


class DocumentDB(DocumentDBAPI):
    def __init__(self):
        super().__init__(settings.PORTALDB_URL)


class DocumentDBAsync(DocumentDBAsyncAPI):
    def __init__(self):
        super().__init__(settings.PORTALDB_URL)


def get_existing_sfids(collection: pymongo.collection.Collection) -> set:
    existing_ids = set()
    cursor = collection.find({}, {"Id": 1})
    for sfobject in cursor:
        existing_ids.add(sfobject["Id"])

    return existing_ids


def get_sf_collection(portaldb: DocumentDB, collection_name: str) -> pymongo.collection.Collection:
    collection = portaldb.client[PORTALDB_DATABASE][collection_name]
    return collection


def sync_sfsimple_to_portaldb(sf: Salesforce,
                              portaldb: DocumentDB,
                              collection_name: str,
                              sfapi_object: str,
                              query_suffix: str = "") -> None:
    portaldb_collection = get_sf_collection(portaldb, collection_name)

    existing_sfids = get_existing_sfids(portaldb_collection)

    new_sfids = set()
    batch = []
    batch_ids = []
    for sfobject in sfapi.get_all(sf, sfapi_object, bulk=True, query_suffix=query_suffix):
        sfobject = sfobject.model_dump(by_alias=True)
        new_sfids.add(sfobject['Id'])
        batch_ids.append(sfobject['Id'])
        batch.append(sfobject)

        if len(batch) > MONGO_BATCH_SIZE:
            portaldb_collection.delete_many({"Id": {"$in": list(batch_ids)}})
            portaldb_collection.insert_many(batch)
            batch.clear()
            batch_ids.clear()

    if batch:
        portaldb_collection.delete_many({"Id": {"$in": list(batch_ids)}})
        portaldb_collection.insert_many(batch)

    zap_sfids = existing_sfids.difference(new_sfids)
    portaldb_collection.delete_many({"Id": {"$in": list(zap_sfids)}})


def make_indices(portaldb: DocumentDB):
    for collection_name, indexes in ALL_COLLECTION_INDEXES.items():
        collection = portaldb.client[PORTALDB_DATABASE][collection_name]
        for index_def in indexes:
            index_keys = index_def['keys']
            index_options = index_def.get('options', {})
            collection.create_index(index_keys, **index_options)
