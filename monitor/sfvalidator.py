import datetime
from lib.settings import settings
from lib import sfimport, sfapi
import json
import psycopg2



RECORD_TYPE_ID_CUSTOMERS_CLIENT = None
RECORD_TYPE_ID_ADVERTISING = None
RECORD_TYPE_ID_MANUFACTURING = None

SURVEY_DATE_FIELDS_IN_ORDER = [  # make sure in order of expected age!
    'AccountUpdatesStartDate',
    'AccountUpdatesEndDate',
    'PanelUpdatesStartDate',
    'PanelUpdatesEndDate',
    'LiveSurveyStartDate',
    'LiveSurveyFirstRequest',
    'LiveSurveySecondRequest',
    'LiveSurveyThirdRequest',
    'LiveSurveyFourthRequest',
    'LiveSurveyEndDate',
    'InsightsStartDate',
]

ACCOUNT_HIERARCHY_ADVERTISING = [
    'Holding Group',
    'Network',
    'Sub-network',
    'Agency Brand',
    'Agency Brand (Sub 1)',
    'With Market',
    'Office',
]
ACCOUNT_HIERARCHY_ADVERTISING_TOP_OF_TREE_VALID = ACCOUNT_HIERARCHY_ADVERTISING[:-2]

ACCOUNT_HIERARCHY_MANUFACTURING = [
    'Company',
    'Division',
    'Sub-division',
    'Sub Segment 1',
    'Sub Segment 2',
    'Country Market',
    'Site',
]
ACCOUNT_HIERARCHY_MANUFACTURING_TOP_OF_TREE_VALID = ACCOUNT_HIERARCHY_MANUFACTURING[:-2]

FAKE_EMAIL_DOMAINS = [
    'unknownemail.com',
    'vantagenoemailprovideddonotmail.com',
    'noemailprovideddonotmail.com',
]

validation_issues = []

def json_serializable(obj):
    """JSON serializer for objects not serializable by default json code"""

    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, datetime.date):
        return obj.isoformat()
    elif isinstance(obj, set):
        return list(obj)
    raise TypeError("Type %s not serializable" % type(obj))


def _find_account_ids(d, account_ids=None):
    if account_ids is None:
        account_ids = set()

    if isinstance(d, dict):
        for k, v in d.items():
            if isinstance(v, str):
                if v.startswith('001') and len(v) == 18:  # salesforce account ids always start with 001
                    account_ids.add(v)
            elif isinstance(v, list):
                account_ids = _find_account_ids(v, account_ids=account_ids)
            elif isinstance(v, dict):
                account_ids = _find_account_ids(v, account_ids=account_ids)

    elif isinstance(d, list):
        for item in d:
            account_ids = _find_account_ids(item, account_ids=account_ids)

    return account_ids


def add_message(core_object_name, message_code, message_level, related_details):
    account_ids = _find_account_ids(related_details)
    related_details['account_ids'] = list(account_ids)

    message = {
        "object_name": core_object_name,
        "code": message_code,
        "level": message_level,
        "related": json.dumps(related_details, default=json_serializable),
    }
    validation_issues.append(message)


def _std_fields(all_sfobjects, o, fields=None, object_prefix=None):
    is_active_round = None

    if fields is None:
        fields = {}

    for fieldname in o.model_fields.keys():
        if object_prefix:
            d = fields.setdefault(object_prefix.lower(), {})
        else:
            d = fields.setdefault(o.__class__.__name__.lower(), {})
        d[fieldname.lower()] = getattr(o, fieldname)

    if isinstance(o, sfapi.SurveyResponse):
        fields.setdefault('surveyresponse', {}).update({'id': o.Id})
        o = all_sfobjects['SurveyPanelMember'].get(o.SurveyPanelMemberId)

    if isinstance(o, sfapi.SurveyPanelMember):
        fields.setdefault('surveypanelmember', {}).update({'id': o.Id})
        fields.setdefault('contact', {}).update({'id': o.ContactId, 'email': o.ContactEmail})

        o = all_sfobjects['SurveyClient'].get(o.SurveyClientId)

    if isinstance(o, sfapi.SurveyPanelManager):
        contact = all_sfobjects['Contact'].get(o.ContactId)

        fields.setdefault('surveypanelmanager', {}).update({'id': o.Id})
        fields.setdefault('contact', {}).update({'id': o.ContactId,
                                                 'email': contact.Email if contact else None})

        o = all_sfobjects['SurveyClient'].get(o.SurveyClientId)

    if isinstance(o, sfapi.SurveyClient):
        fields.setdefault('surveyclient', {}).update({'id': o.Id,
                                                      'name': o.Name,
                                                      'customersclientid': o.CustomersClientId})

        o = all_sfobjects['CustomerSurvey'].get(o.CustomerSurveyId)

    if isinstance(o, sfapi.SurveyAccountManager):
        contact = all_sfobjects['Contact'].get(o.ContactId)

        fields.setdefault('surveyaccountmanager', {}).update({'id': o.Id})
        fields.setdefault('contact', {}).update({'id': o.ContactId,
                                                 'email': contact.Email if contact else None})

        o = all_sfobjects['CustomerSurvey'].get(o.CustomerSurveyId)

    if isinstance(o, sfapi.CustomerSurvey):
        fields.setdefault('customersurvey', {}).update({'id': o.Id,
                                                        'name': o.Name,
                                                        'customerid': o.CustomerId})

        if is_active_round is None and o.AccountUpdatesStartDate and o.InsightsStartDate:
            is_active_round = o.AccountUpdatesStartDate <= datetime.date.today() <= o.InsightsStartDate

        o = all_sfobjects['CustomerSurveyRound'].get(o.CustomerSurveyRoundId)

    if isinstance(o, sfapi.CustomerSurveyRound):
        fields.setdefault('customersurveyround', {}).update({'id': o.Id,
                                                             'name': o.Name,
                                                             'accountid': o.AccountId})

        if is_active_round is None and o.AccountUpdatesStartDate and o.InsightsStartDate:
            is_active_round = o.AccountUpdatesStartDate <= datetime.date.today() <= o.InsightsStartDate

        o = all_sfobjects['SurveyRound'].get(o.SurveyRoundId)

    if isinstance(o, sfapi.SurveyRound):
        fields.setdefault('surveyround', {}).update({'id': o.Id,
                                                     'name': o.Name})

        if is_active_round is None and o.RoundDate and o.RoundEndDate:
            is_active_round = o.RoundDate <= datetime.date.today() <= o.RoundEndDate

    if is_active_round is not None:
        fields['is_active_round'] = is_active_round

    return fields


def _check_survey_dates_are_in_order(type, instance, message_fields):
    date_field_values = [getattr(instance, x) for x in SURVEY_DATE_FIELDS_IN_ORDER]
    date_field_values = [x for x in date_field_values if x is not None]

    previous_date_value = date_field_values[0]
    for i in range(1, len(date_field_values)):
        if date_field_values[i] < previous_date_value:
            m = {'date_field_name': SURVEY_DATE_FIELDS_IN_ORDER[i],
                 'previous_date_value': previous_date_value,
                 'current_date_value': date_field_values[i]}
            m.update(message_fields)
            add_message(str(type), f"{str(type)}-dates-not-in-order", "error", m)
            break
        previous_date_value = date_field_values[i]


def _check_account_is_customer_client(all_sfobjects, account_id):
    account = all_sfobjects['Account'][account_id]
    return account.RecordTypeId in {RECORD_TYPE_ID_CUSTOMERS_CLIENT}


def _check_account_is_crc_client(all_sfobjects, account_id):
    account = all_sfobjects['Account'][account_id]
    return account.RecordTypeId in {RECORD_TYPE_ID_ADVERTISING, RECORD_TYPE_ID_MANUFACTURING}


def check_accounts_are_correct(all_sfobjects):
    for account in all_sfobjects['Account'].values():

        root_account = account
        while root_account.ParentId:
            if root_account.RecordTypeId != account.RecordTypeId:
                add_message('Account', 'Account-RecordTypeId-mismatch', 'error',
                            _std_fields(all_sfobjects, account,
                                        {'root_account_id': root_account.Id,
                                         'root_account_name': root_account.Name,
                                         'root_account_record_type_id': root_account.RecordTypeId}))

            root_account = all_sfobjects['Account'][root_account.ParentId]

        if root_account.Id != account.Id and root_account.Id != account.UltimateParent:
            add_message('Account', 'Account-UltimateParent-mismatch', 'error',
                        _std_fields(all_sfobjects, account,
                                    {'root_account_id': root_account.Id,
                                     'root_account_name': root_account.Name,
                                     'ultimate_parent_id': account.UltimateParent}))

        if account.ParentId is not None and account.TopOfHierarchy:
            add_message('Account', 'Account-TopOfHierarchy-mismatch', 'error',
                        _std_fields(all_sfobjects, account,
                                    {'top_of_hierarchy': account.TopOfHierarchy}))

        # FIXME: what is Survey_Account_Manager__c on Account??? is it still used? don't really see how it can work

        # FIXME: validate market of account somehow maybe? can maybe be linked to hierarchy level?


def check_contacts_are_correct(all_sfobjects):
    for contact in all_sfobjects['Contact'].values():
        account = all_sfobjects['Account'].get(contact.AccountId)
        email_domain = contact.Email.split('@', 1)[1] if contact.Email else None

        if contact.Signatory and not contact.Signature:
            add_message('Contact', 'Contact-with-signature-but-not-signatory', 'warning',
                        _std_fields(all_sfobjects, contact))

        if account and account.RecordTypeId == RECORD_TYPE_ID_CUSTOMERS_CLIENT:
            if contact.TeamId is not None:
                add_message('Contact', 'Contact-CustomerClient-ContactInTeam', 'error',
                            _std_fields(all_sfobjects, contact))

        if contact.DisableExternalCommunications and email_domain not in FAKE_EMAIL_DOMAINS:
            add_message('Contact', 'Contact-DisableExternalCommunication', 'warning',
                        _std_fields(all_sfobjects, contact))


def check_customerclientrelationships_are_correct(all_sfobjects):
    for ccr in all_sfobjects['CustomerClientRelationship'].values():
        if ccr.CustomersClientAccountId is None:
            add_message('CustomerClientRelationship', 'CustomerClientRelationship-CustomersClientAccountId-missing', 'error',
                        _std_fields(all_sfobjects, ccr))

        elif not _check_account_is_customer_client(all_sfobjects, ccr.CustomersClientAccountId):
            add_message('CustomerClientRelationship', 'CustomerClientRelationship-CustomersClientAccountId-not-customers-client', 'error',
                        _std_fields(all_sfobjects, ccr))

        if ccr.CustomerAccountId is None:
            add_message('CustomerClientRelationship', 'CustomerClientRelationship-CustomerAccountId-missing', 'error',
                        _std_fields(all_sfobjects, ccr))

        elif not _check_account_is_crc_client(all_sfobjects, ccr.CustomerAccountId):
            add_message('CustomerClientRelationship', 'CustomerClientRelationship-CustomerAccountId-not-advertising-or-manufacturing', 'error',
                        _std_fields(all_sfobjects, ccr))

        if ccr.AccountGroupId:
            account_group = all_sfobjects['AccountGroup'][ccr.AccountGroupId]
            if account_group.Id != ccr.CustomerAccountId:
                add_message('CustomerClientRelationship', 'CustomerClientRelationship-AccountGroup-not-part-of-agency-account', 'error',
                            _std_fields(all_sfobjects, ccr))


def check_divisions_are_correct(all_sfobjects):
    for division in all_sfobjects['Division'].values():
        if division.AccountId is None:
            add_message('Division', 'Division-AccountId-missing', 'error',
                        _std_fields(all_sfobjects, division))
        elif not _check_account_is_crc_client(all_sfobjects, division.AccountId):
            add_message('Division', 'Division-AccountId-not-advertising-or-manufacturing', 'error',
                        _std_fields(all_sfobjects, division))


def check_markets_are_correct(all_sfobjects):
    for market in all_sfobjects['Market'].values():
        if market.MarketType is None:
            add_message('Market', 'Market-MarketType-missing', 'error',
                        _std_fields(all_sfobjects, market))


def check_teams_are_correct(all_sfobjects, account_forest_by_id):
    for team in all_sfobjects['Team'].values():
        account = all_sfobjects['Account'].get(team.AccountId)

        if team.AccountId is None:
            add_message("Team", "Team-AccountId-missing", "error",
                        _std_fields(all_sfobjects, team))
        elif not _check_account_is_crc_client(all_sfobjects, team.AccountId):
            add_message("Team", "Team-AccountId-not-advertising-or-manufacturing", "error",
                        _std_fields(all_sfobjects, team))

        if team.MarketId is None:
            add_message("Team", "Team-Market-missing", "error",
                        _std_fields(all_sfobjects, team))

        if account:
            account_and_children = account_forest_by_id.get(account.Id)
            if account_and_children and account_and_children.get('children'):
                add_message("Team", "Team-Account-not-lowest-level", "error",
                            _std_fields(all_sfobjects, team))


def check_accountgroups_are_correct(all_sfobjects):
    for account_group in all_sfobjects['AccountGroup'].values():
        if account_group.AccountId is None:
            add_message('AccountGroup', 'AccountGroup-AccountId-missing', 'error',
                        _std_fields(all_sfobjects, account_group))

        elif not _check_account_is_crc_client(all_sfobjects, account_group.AccountId):
            add_message('AccountGroup', 'AccountGroup-AccountId-not-advertising-or-manufacturing',
                        'error',
                        _std_fields(all_sfobjects, account_group))


def check_accountcontactrelations_are_correct(all_sfobjects):
    for acr in all_sfobjects['AccountContactRelation'].values():
        if acr.ContactId is None:
            add_message("AccountContactRelation", "AccountContactRelation-Contact-missing", "error",
                        _std_fields(all_sfobjects, acr))
        if acr.CustomerAccountId is None:
            add_message("AccountContactRelation", "AccountContactRelation-CustomerAccountId-missing", "error",
                        _std_fields(all_sfobjects, acr))

        # FIXME: is there validation we can do here? not sure what type of account this should be!


def check_surveyrounds_are_correct(all_sfobjects):
    for sr in all_sfobjects['SurveyRound'].values():
        if sr.RoundEndDate < sr.RoundDate:
            add_message('SurveyRound', 'SurveyRound-EndDate-before-StartDate', 'error',
                        _std_fields(all_sfobjects, sr))

        if sr.DisableExternalCommunication and sr.RoundDate and sr.RoundEndDate:
            if sr.RoundDate <= datetime.date.today() <= sr.RoundEndDate:
                add_message('SurveyRound', 'SurveyRound-DisableExternalCommunication', 'warning',
                            _std_fields(all_sfobjects, sr))


def check_customersurveyrounds_are_correct(all_sfobjects):
    for csr in all_sfobjects['CustomerSurveyRound'].values():
        if csr.AccountId is None:
            add_message('CustomerSurveyRound', 'CustomerSurveyRound-Account-missing', 'error',
                        _std_fields(all_sfobjects, csr))

        elif not _check_account_is_crc_client(all_sfobjects, csr.AccountId):
            add_message('CustomerSurveyRound', 'CustomerSurveyRound-Account-not-advertising-or-manufacturing', 'error',
                        _std_fields(all_sfobjects, csr))

        if csr.SurveyRoundId is None:
            add_message('CustomerSurveyRound', 'CustomerSurveyRound-SurveyRound-missing', 'error',
                        _std_fields(all_sfobjects, csr))

        account = all_sfobjects['Account'].get(csr.AccountId)
        if account and account.RecordTypeId == RECORD_TYPE_ID_ADVERTISING:
            if account.OrganisationalLevel not in ACCOUNT_HIERARCHY_ADVERTISING_TOP_OF_TREE_VALID:
                add_message('CustomerSurveyRound', 'CustomerSurveyRound-OrganisationalLevel-not-top-of-tree', 'warning',
                            _std_fields(all_sfobjects, csr, {'organisational_level': account.OrganisationalLevel,
                                                           'allowed_hierarchy': ACCOUNT_HIERARCHY_ADVERTISING_TOP_OF_TREE_VALID}))

        elif account and account.RecordTypeId == RECORD_TYPE_ID_MANUFACTURING:
            if account.OrganisationalLevel not in ACCOUNT_HIERARCHY_MANUFACTURING_TOP_OF_TREE_VALID:
                add_message('CustomerSurveyRound', 'CustomerSurveyRound-OrganisationalLevel-not-top-of-tree', 'warning',
                            _std_fields(all_sfobjects, csr, {'organisational_level': account.OrganisationalLevel,
                                                           'allowed_hierarchy': ACCOUNT_HIERARCHY_MANUFACTURING_TOP_OF_TREE_VALID}))

        _check_survey_dates_are_in_order('CustomerSurveyRound', csr, _std_fields(all_sfobjects, csr))

        if csr.DisableExternalCommunication and csr.AccountUpdatesStartDate and csr.LiveSurveyEndDate:
            if csr.AccountUpdatesStartDate <= datetime.date.today() <= csr.LiveSurveyEndDate:
                add_message('CustomerSurveyRound', 'CustomerSurveyRound-DisableExternalCommunication', 'warning',
                            _std_fields(all_sfobjects, csr))


def check_customersurveys_are_correct(all_sfobjects):
    for cs in all_sfobjects['CustomerSurvey'].values():
        if cs.CustomerId is None:
            add_message('CustomerSurvey', 'CustomerSurvey-Customer-missing', 'error',
                        _std_fields(all_sfobjects, cs))

        elif not _check_account_is_crc_client(all_sfobjects, cs.CustomerId):
            add_message('CustomerSurvey', 'CustomerSurvey-Customer-not-advertising-or-manufacturing', 'error',
                        _std_fields(all_sfobjects, cs))

        if cs.CustomerSurveyRoundId is None:
            add_message("CustomerSurvey", "CustomerSurvey-CustomerSurveyRound-missing", "error",
                        _std_fields(all_sfobjects, cs))

        if cs.CustomerSurveyRoundId:
            customer_survey_round = all_sfobjects['CustomerSurveyRound'][cs.CustomerSurveyRoundId]
            csr_account = all_sfobjects['Account'][customer_survey_round.AccountId]

            # FIXME: validate CustomerId is for correct holding group

        _check_survey_dates_are_in_order('CustomerSurvey', cs, _std_fields(all_sfobjects, cs))

        if cs.InsightsStartDate and cs.InsightsStartDate <= datetime.date.today() and cs.Stage != 'Insights':
            add_message('CustomerSurvey', 'CustomerSurvey-Stage-NotInsights', 'error',
                        _std_fields(all_sfobjects, cs))

        if cs.AccountUpdatesStartDate is None or \
            cs.AccountUpdatesEndDate is None or \
            cs.PanelUpdatesStartDate is None or \
            cs.PanelUpdatesEndDate is None or \
            cs.LiveSurveyStartDate is None or \
            cs.LiveSurveyFirstRequest is None or \
            cs.LiveSurveySecondRequest is None or \
            cs.LiveSurveyThirdRequest is None or \
            cs.LiveSurveyFourthRequest is None or \
            cs.LiveSurveyEndDate is None or \
            cs.InsightsStartDate is None:

            details = _std_fields(all_sfobjects, cs)
            for fieldname in SURVEY_DATE_FIELDS_IN_ORDER:
                if getattr(cs, fieldname) is None:
                    details['date_field_name'] = fieldname
                    break
            add_message('CustomerSurvey', 'CustomerSurvey-MissingDates', 'warning', details)
            continue

        if cs.DisableExternalCommunication and cs.AccountUpdatesStartDate <= datetime.date.today() <= cs.LiveSurveyEndDate:
            add_message('CustomerSurvey', 'CustomerSurvey-DisableExternalCommunication', 'warning',
                        _std_fields(all_sfobjects, cs))

        if cs.AccountUpdatesStartDate <= datetime.date.today() <= cs.AccountUpdatesEndDate and cs.Stage != 'Survey Setup':
            add_message('CustomerSurvey', 'CustomerSurvey-Stage-NotSurveySetup', 'error',
                        _std_fields(all_sfobjects, cs))

        if cs.LiveSurveyStartDate <= datetime.date.today() <= cs.LiveSurveyEndDate and cs.Stage != 'Live Survey':
            add_message('CustomerSurvey', 'CustomerSurvey-Stage-NotLiveSurvey', 'error',
                        _std_fields(all_sfobjects, cs))


        if cs.AccountUpdatesEndDate <= datetime.date.today() <= cs.PanelUpdatesStartDate and cs.State != 'Survey Clients Agreed':
            add_message('CustomerSurvey', 'CustomerSurvey-State-NotSurveyClientsAgreed', 'error',
                        _std_fields(all_sfobjects, cs))

        if cs.PanelUpdatesEndDate <= datetime.date.today() <= cs.LiveSurveyEndDate and cs.State != 'Panel Agreed':
            add_message('CustomerSurvey', 'CustomerSurvey-State-NotPanelAgreed', 'error',
                        _std_fields(all_sfobjects, cs))


def check_surveypanelmanagers_are_correct(all_sfobjects):
    for spm in all_sfobjects['SurveyPanelManager'].values():
        if spm.ContactId is None:
            add_message('SurveyPanelManager', 'SurveyPanelManager-Contact-missing', 'error',
                        _std_fields(all_sfobjects, spm))

        if spm.SurveyClientId is None:
            add_message('SurveyPanelManager', 'SurveyPanelManager-SurveyClient-missing', 'error',
                        _std_fields(all_sfobjects, spm))

        # FIXME: validate SPM is for correct holding group


def check_surveypanelmembers_are_correct(all_sfobjects):
    for spm in all_sfobjects['SurveyPanelMember'].values():
        if spm.ContactId is None:
            add_message('SurveyPanelMember', 'SurveyPanelMember-Contact-missing', 'error',
                        _std_fields(all_sfobjects, spm))


        if spm.SurveyClientId is None:
            add_message("SurveyPanelMember", "SurveyPanelMember-SurveyClient-missing", "error",
                        _std_fields(all_sfobjects, spm))

        # FIXME: validate SPM is for correct customer's client


def check_surveyresponses_are_correct(all_sfobjects):
    for sr in all_sfobjects['SurveyResponse'].values():
        if sr.SurveyPanelMemberId is None:
            add_message("SurveyResponse", "SurveyResponse-SurveyPanelMember-missing", "error",
                        _std_fields(all_sfobjects, sr))


def check_surveyaccountmanagers_are_correct(all_sfobjects):
    for sam in all_sfobjects['SurveyAccountManager'].values():
        if sam.ContactId is None:
            add_message('SurveyAccountManager', 'SurveyAccountManager-Contact-missing', 'error',
                        _std_fields(all_sfobjects, sam))

        if sam.CustomerSurveyId is None:
            add_message('SurveyAccountManager', 'SurveyAccountManager-CustomerSurvey-missing', 'error',
                        _std_fields(all_sfobjects, sam))

        # FIXME: validate holding group


def check_surveyclients_are_correct(all_sfobjects):
    for sc in all_sfobjects['SurveyClient'].values():
        cs = all_sfobjects['CustomerSurvey'].get(sc.CustomerSurveyId)

        if sc.CustomersClientId is None:
            add_message('SurveyClient', 'SurveyClient-CustomersClient-missing', 'error',
                        _std_fields(all_sfobjects, sc))

        elif not _check_account_is_customer_client(all_sfobjects, sc.CustomersClientId):
            add_message('SurveyClient', 'SurveyClient-CustomersClient-not-customers-client', 'error',
                        _std_fields(all_sfobjects, sc))

        if sc.CustomerSurveyId is None:
            add_message('SurveyClient', 'SurveyClient-CustomerSurvey-missing', 'error',
                        _std_fields(all_sfobjects, sc))

        # skip further checks if key dates are missing
        if cs.PanelUpdatesStartDate is None or cs.PanelUpdatesEndDate is None or \
            cs.LiveSurveyStartDate is None or cs.LiveSurveyEndDate is None:
            continue

        if cs.PanelUpdatesStartDate <= datetime.date.today() <= cs.PanelUpdatesEndDate and sc.State != 'Panel Pending':
            add_message('SurveyClient', 'SurveyClient-State-NotPanelPending', 'error',
                        _std_fields(all_sfobjects, sc))

        if cs.PanelUpdatesEndDate <= datetime.date.today() <= cs.LiveSurveyStartDate and cs.State == 'Panel Agreed' and sc.State != 'Panel Agreed':
            add_message('SurveyClient', 'SurveyClient-State-NotPanelAgreed', 'error',
                        _std_fields(all_sfobjects, sc))

        if cs.PanelUpdatesEndDate <= datetime.date.today() <= cs.LiveSurveyStartDate and cs.Stage == 'Live Survey' and sc.State != 'Survey Ready':
            add_message('SurveyClient', 'SurveyClient-State-NotSurveyReady', 'error',
                        _std_fields(all_sfobjects, sc))

        if cs.LiveSurveyStartDate <= datetime.date.today() <= cs.LiveSurveyEndDate and sc.State != 'Survey Out':
            add_message('SurveyClient', 'SurveyClient-State-NotSurveyOut', 'error',
                        _std_fields(all_sfobjects, sc))

        # FIXME: validate holding group


def check_contact_survey_roles(all_sfobjects):
    contacts_who_need_account_manager = {}
    for sam in all_sfobjects['SurveyAccountManager'].values():
        if sam.ContactId is None:
            continue
        contact = all_sfobjects['Contact'][sam.ContactId]
        if not contact.AccountsManager:
            contacts_who_need_account_manager.setdefault(contact.Id, set())
            contacts_who_need_account_manager[contact.Id].add(sam.CustomerSurveyId)

    for cid in contacts_who_need_account_manager:
        contact = all_sfobjects['Contact'][cid]
        add_message('Contact', 'Contact-AccountManager-role-missing', 'error',
                    _std_fields(all_sfobjects, contact, {'role': 'AccountManager', 'customer_survey_ids': contacts_who_need_account_manager[cid]}))

    contacts_who_need_panel_manager = {}
    for spm in all_sfobjects['SurveyPanelManager'].values():
        if spm.ContactId is None:
            continue
        contact = all_sfobjects['Contact'][spm.ContactId]
        if not contact.PanelManager:
            contacts_who_need_panel_manager.setdefault(contact.Id, set())
            contacts_who_need_panel_manager[contact.Id].add(spm.SurveyClientId)

    for cid in contacts_who_need_panel_manager:
        contact = all_sfobjects['Contact'][cid]
        add_message("Contact", "Contact-PanelManager-role-missing", "error",
                    _std_fields(all_sfobjects, contact, {'role': 'PanelManager', 'survey_client_ids': contacts_who_need_panel_manager[cid]}))

    contacts_who_need_signatory = {}
    for sc in all_sfobjects['SurveyClient'].values():
        if sc.SignatoryId is None:
            continue
        contact = all_sfobjects['Contact'][sc.SignatoryId]
        if not contact.Signatory:
            contacts_who_need_signatory.setdefault(contact.Id, set())
            contacts_who_need_signatory[contact.Id].add(sc.Id)

    for cid in contacts_who_need_signatory:
        contact = all_sfobjects['Contact'][cid]
        add_message("Contact", "Contact-Signatory-role-missing", "error",
                    _std_fields(all_sfobjects, contact, {'role': 'Signatory', 'survey_client_ids': contacts_who_need_signatory[cid]}))


def check_customersurvey_dates_are_sane(all_sfobjects):
    for cs in all_sfobjects['CustomerSurvey'].values():
        if cs.CustomerSurveyRoundId is None:
            continue

        customer_survey_round = all_sfobjects['CustomerSurveyRound'][cs.CustomerSurveyRoundId]
        dates = [getattr(customer_survey_round, x) for x in SURVEY_DATE_FIELDS_IN_ORDER]
        dates = [x for x in dates if x is not None]
        min_date = min(dates)
        max_date = max(dates)
        for date_field in SURVEY_DATE_FIELDS_IN_ORDER:
            date_value = getattr(cs, date_field)
            if not date_value:
                continue
            if date_value < min_date - datetime.timedelta(days=90) or date_value > max_date + datetime.timedelta(days=90):
                add_message("CustomerSurvey", "CustomerSurvey-dates-not-sane", "error",
                            _std_fields(all_sfobjects, cs, {'date_field_name': date_field,
                                                           'min_date': min_date,
                                                           'date_value': date_value,
                                                           'max_date': max_date}))
                continue


def check_customersurveyround_dates_are_sane(all_sfobjects):
    for csr in all_sfobjects['CustomerSurveyRound'].values():
        if csr.SurveyRoundId is None:
            continue

        survey_round = all_sfobjects['SurveyRound'][csr.SurveyRoundId]
        dates = [getattr(survey_round, x) for x in ['RoundDate', 'RoundEndDate']]
        dates = [x for x in dates if x is not None]
        min_date = min(dates)
        max_date = max(dates)
        for date_field in SURVEY_DATE_FIELDS_IN_ORDER:
            date_value = getattr(csr, date_field)
            if not date_value:
                continue
            if date_value < min_date - datetime.timedelta(days=90) or date_value > max_date + datetime.timedelta(days=90):
                add_message("CustomerSurveyRound", "CustomerSurveyRound-dates-not-sane", "error",
                            _std_fields(all_sfobjects, csr, {'date_field_name': date_field,
                                                           'min_date': min_date,
                                                           'date_value': date_value,
                                                           'max_date': max_date}))
                continue


def check_for_duplicate_business_keys(all_sfobjects):
    for objname, sfobjects in all_sfobjects.items():
        bks = {}
        for o in sfobjects.values():
            bk = o.business_key2()
            bks.setdefault(bk, [])
            bks[bk].append(o)

        for bk, instances in bks.items():
            if len(instances) > 1:
                fields = {'business_key': bk,
                          'instances': [x.Id for x in instances]}

                add_message(objname, f"{objname}-duplicate-business-key", "error",
                            _std_fields(all_sfobjects, instances[0], fields))


def check_accountcontactrelations_are_present(all_sfobjects):
    existing_acrs_bk_lookup = {}
    for acr in all_sfobjects['AccountContactRelation'].values():
        key = f"{acr.ContactId}_{acr.CustomerAccountId}"
        existing_acrs_bk_lookup.setdefault(key, [])
        existing_acrs_bk_lookup[key].append(acr)

    seen_duff_bks = set()
    for spm in all_sfobjects['SurveyPanelMember'].values():
        survey_client = all_sfobjects['SurveyClient'][spm.SurveyClientId]
        customer_survey = all_sfobjects['CustomerSurvey'][survey_client.CustomerSurveyId]
        key = f"{spm.ContactId}_{customer_survey.CustomerId}"

        if key not in existing_acrs_bk_lookup and key not in seen_duff_bks:
            contact = all_sfobjects['Contact'][spm.ContactId]
            agency = all_sfobjects['Account'][customer_survey.CustomerId]

            fields = _std_fields(all_sfobjects, contact)
            fields = _std_fields(all_sfobjects, agency, fields)
            fields = _std_fields(all_sfobjects, spm, fields)

            # ACRs are not associated with any particular survey round, but this gets accidentally set
            # as it is a survey panel member object
            fields['is_active_round'] = None

            add_message('AccountContactRelation', 'AccountContactRelation-missing', 'error',
                        fields)
            seen_duff_bks.add(key)


def check_customerclientrelationships_are_present(all_sfobjects):
    existing_ccrs_bk_lookup = {}
    for ccr in all_sfobjects['CustomerClientRelationship'].values():
        key = f"{ccr.CustomersClientAccountId}_{ccr.CustomerAccountId}"
        existing_ccrs_bk_lookup.setdefault(key, [])
        existing_ccrs_bk_lookup[key].append(ccr)

    seen_duff_bks = set()
    for sc in all_sfobjects['SurveyClient'].values():
        customer_survey = all_sfobjects['CustomerSurvey'][sc.CustomerSurveyId]
        key = f"{sc.CustomersClientId}_{customer_survey.CustomerId}"

        if key not in existing_ccrs_bk_lookup and key not in seen_duff_bks:
            customer_client = all_sfobjects['Account'][sc.CustomersClientId]
            agency = all_sfobjects['Account'][customer_survey.CustomerId]

            fields = _std_fields(all_sfobjects, customer_client, object_prefix='customer_client')
            fields = _std_fields(all_sfobjects, agency, fields)
            fields = _std_fields(all_sfobjects, sc, fields)

            add_message('CustomerClientRelationship', 'CustomerClientRelationship-missing', 'error',
                        fields)
            seen_duff_bks.add(key)


def check_email_domain_record_type_consistency(all_sfobjects):
    account_record_types = {}
    for acr in all_sfobjects['AccountRecordType'].values():
        account_record_types[acr.Id] = acr

    contact_by_email = {}
    account_by_email_domain = {}
    for contact in all_sfobjects['Contact'].values():
        email_address = contact.Email.strip().lower()
        email_domain = email_address.split('@', 1)[1]
        contact_by_email[email_address] = contact

        account = all_sfobjects['Account'][contact.AccountId]
        account_by_email_domain.setdefault(email_domain, {})
        account_by_email_domain[email_domain].setdefault(account.Id, set())
        account_by_email_domain[email_domain][account.Id].add(contact.Email)

    for email_domain, account_contact_emails in account_by_email_domain.items():
        account_types = {}
        for account_id, contact_emails in account_contact_emails.items():
            account = all_sfobjects['Account'][account_id]
            record_type = account_record_types[account.RecordTypeId]
            account_types.setdefault(record_type.Name, set())
            account_types[record_type.Name] |= set(contact_emails)

        if len(account_types) > 1:
            add_message('Contact', 'Contact-MultipleAccountRecordTypes', 'warning',
                        {'email_domain': email_domain,
                         'account_types': account_types})


def check_email_domain_top_level_account_consistency(all_sfobjects):
    contact_by_email = {}
    top_level_account_by_email_domain = {}
    for contact in all_sfobjects['Contact'].values():
        email_address = contact.Email.strip().lower()
        email_domain = email_address.split('@', 1)[1]
        contact_by_email[email_address] = contact

        # skip special emails domains
        if email_domain in FAKE_EMAIL_DOMAINS:
            continue

        root_account = all_sfobjects['Account'][contact.AccountId]
        while root_account.ParentId:
            root_account = all_sfobjects['Account'][root_account.ParentId]
        top_level_account_by_email_domain.setdefault(email_domain, {})
        top_level_account_by_email_domain[email_domain].setdefault(root_account.Name, set())
        top_level_account_by_email_domain[email_domain][root_account.Name].add(contact.Email)

    for email_domain, top_level_account_contact_emails in top_level_account_by_email_domain.items():
        if len(top_level_account_contact_emails) > 1:
            add_message('Contact', 'Contact-MultipleTopLevelAccounts', 'warning',
                        {'email_domain': email_domain,
                         'account_types': top_level_account_contact_emails})


def find_account_record_types(all_sfobjects):
    global RECORD_TYPE_ID_CUSTOMERS_CLIENT, RECORD_TYPE_ID_ADVERTISING, RECORD_TYPE_ID_MANUFACTURING

    for acr in all_sfobjects['AccountRecordType'].values():
        if acr.Name == sfimport.RECORD_TYPE_CUSTOMERS_CLIENT_ACCOUNT:
            RECORD_TYPE_ID_CUSTOMERS_CLIENT = acr.Id
        elif acr.Name == sfimport.RECORD_TYPE_CRCS_CUSTOMER_ADVERTISING:
            RECORD_TYPE_ID_ADVERTISING = acr.Id
        elif acr.Name == sfimport.RECORD_TYPE_CRCS_CUSTOMER_MANUFACTURING:
            RECORD_TYPE_ID_MANUFACTURING = acr.Id


def check_contact_report_views_are_correct(all_sfobjects):
    for crv in all_sfobjects['ContactReportView'].values():
        if crv.ContactId is None:
            add_message("ContactReportView", "ContactReportView-Contact-missing", "error",
                        _std_fields(all_sfobjects, crv))


def check_contact_key_accounts_are_correct(all_sfobjects):
    for cka in all_sfobjects['ContactKeyAccount'].values():
        if cka.ContactReportViewId is None:
            add_message('ContactKeyAccount', 'ContactKeyAccount-ContactReportView-missing', 'error',
                        _std_fields(all_sfobjects, cka))
        if cka.AccountId is None:
            add_message('ContactKeyAccount', 'ContactKeyAccount-Account-missing', 'error',
                        _std_fields(all_sfobjects, cka))


def check_contact_key_markets_are_correct(all_sfobjects):
    for ckm in all_sfobjects['ContactKeyMarket'].values():
        if ckm.ContactReportViewId is None:
            add_message('ContactKeyMarket', 'ContactKeyMarket-ContactReportView-missing', 'error',
                        _std_fields(all_sfobjects, ckm))

        if ckm.Market is None:
            add_message('ContactKeyMarket', 'ContactKeyMarket-Market-missing', 'error',
                        _std_fields(all_sfobjects, ckm))

def check_contact_key_customers_are_correct(all_sfobjects):
    for ckc in all_sfobjects['ContactKeyCustomer'].values():
        if ckc.ContactReportViewId is None:
            add_message('ContactKeyCustomer', 'ContactKeyCustomer-ContactReportView-missing', 'error',
                        _std_fields(all_sfobjects, ckc))

        if ckc.CustomerId is None:
            add_message('ContactKeyCustomer', 'ContactKeyCustomer-Customer-missing', 'error',
                        _std_fields(all_sfobjects, ckc))


def check_contact_key_teams_are_correct(all_sfobjects):
    for ckt in all_sfobjects['ContactKeyTeam'].values():
        if ckt.ContactReportViewId is None:
            add_message('ContactKeyTeam', 'ContactKeyTeam-ContactReportView-missing', 'error',
                        _std_fields(all_sfobjects, ckt))

        if ckt.TeamId is None:
            add_message('ContactKeyTeam', 'ContactKeyTeam-Team-missing', 'error',
                        _std_fields(all_sfobjects, ckt))


def check_contact_reporting_categories_are_correct(all_sfobjects):
    for crc in all_sfobjects['ContactReportingCategory'].values():
        if crc.ContactReportViewId is None:
            add_message('ContactReportingCategory', 'ContactReportingCategory-ContactReportView-missing', 'error',
                        _std_fields(all_sfobjects, crc))


def check_customerclient_contacts_not_in_team(all_sfobjects):
    for contact in all_sfobjects['Contact'].values():
        account = all_sfobjects['Account'][contact.AccountId]
        if not account:
            continue
        if account.RecordTypeId != RECORD_TYPE_ID_CUSTOMERS_CLIENT:
            continue
        if contact.TeamId is not None:
            add_message('Contact', 'Contact-CustomerClient-ContactInTeam', 'error',
                        _std_fields(all_sfobjects, contact))


def _validate_account_hierarchy(all_sfobjects, account_id, account_forest_by_id, hierarchy):
    account_details = account_forest_by_id[account_id]
    if account_details['account'].OrganisationalLevel not in hierarchy:
        add_message('Account', 'Account-OrganisationalLevel-mismatch', 'error',
                    _std_fields(all_sfobjects, account_details['account'],
                                {'organisational_level': account_details['account'].OrganisationalLevel,
                                 'expected_hierarchy': hierarchy}))

    for child_account in account_details['children']:
        _validate_account_hierarchy(all_sfobjects, child_account.Id, account_forest_by_id, hierarchy[1:])


def check_account_hierarchy(all_sfobjects, root_accounts, account_forest_by_id):
    for root_account in root_accounts:
        if root_account.RecordTypeId == RECORD_TYPE_ID_CUSTOMERS_CLIENT:
            continue  # just ignore these

        elif root_account.RecordTypeId == RECORD_TYPE_ID_ADVERTISING:
            hierarchy = ACCOUNT_HIERARCHY_ADVERTISING

            if root_account.OrganisationalLevel not in ACCOUNT_HIERARCHY_ADVERTISING_TOP_OF_TREE_VALID:
                add_message('Account', 'Account-OrganisationalLevel-top-of-tree-mismatch', 'error',
                             _std_fields(all_sfobjects, root_account,
                                         {'organisational_level': root_account.OrganisationalLevel,
                                          'allowed_hierarchy': ACCOUNT_HIERARCHY_ADVERTISING_TOP_OF_TREE_VALID}))

        elif root_account.RecordTypeId == RECORD_TYPE_ID_MANUFACTURING:
            hierarchy = ACCOUNT_HIERARCHY_MANUFACTURING

            if root_account.OrganisationalLevel not in ACCOUNT_HIERARCHY_MANUFACTURING_TOP_OF_TREE_VALID:
                add_message('Account', 'Account-OrganisationalLevel-top-of-tree-mismatch', 'error',
                            _std_fields(all_sfobjects, root_account,
                                        {'organisational_level': root_account.OrganisationalLevel,
                                         'allowed_hierarchy': ACCOUNT_HIERARCHY_MANUFACTURING_TOP_OF_TREE_VALID}))

        else:
            raise ValueError(f"Unknown AccountTypeId {root_account.RecordTypeId} for account {root_account.Id}")

        _validate_account_hierarchy(all_sfobjects, root_account.Id, account_forest_by_id, hierarchy)


def check_empty_customersurveys(all_sfobjects):
    customer_survey_account_manager_size = {}
    for survey_account_manager in all_sfobjects['SurveyAccountManager'].values():
        customer_survey_account_manager_size.setdefault(survey_account_manager.CustomerSurveyId, 0)
        customer_survey_account_manager_size[survey_account_manager.CustomerSurveyId] += 1

    customer_survey_client_size = {}
    for survey_client in all_sfobjects['SurveyClient'].values():
        customer_survey_client_size.setdefault(survey_client.CustomerSurveyId, 0)
        customer_survey_client_size[survey_client.CustomerSurveyId] += 1

    for customer_survey in all_sfobjects['CustomerSurvey'].values():
        if customer_survey.CustomerId is None or customer_survey.AccountUpdatesStartDate is None or customer_survey.AccountUpdatesEndDate is None or customer_survey.LiveSurveyEndDate is None:
            continue

        if customer_survey.AccountUpdatesStartDate <= datetime.date.today() <= customer_survey.LiveSurveyEndDate:
            if customer_survey_account_manager_size.get(customer_survey.Id, 0) == 0:
                add_message('CustomerSurvey', 'CustomerSurvey-NoAccountManagers', 'warning',
                            _std_fields(all_sfobjects, customer_survey))

        if customer_survey.AccountUpdatesEndDate <= datetime.date.today() <= customer_survey.LiveSurveyEndDate:
            if customer_survey_client_size.get(customer_survey.Id, 0) == 0:
                add_message('CustomerSurvey', 'CustomerSurvey-EmptyCustomerSurvey', 'warning',
                            _std_fields(all_sfobjects, customer_survey))


def check_empty_surveyclients(all_sfobjects):
    survey_client_panel_size = {}
    for survey_panel_member in all_sfobjects['SurveyPanelMember'].values():
        survey_client_panel_size.setdefault(survey_panel_member.SurveyClientId, 0)
        survey_client_panel_size[survey_panel_member.SurveyClientId] += 1

    survey_client_managers_size = {}
    for survey_panel_manager in all_sfobjects['SurveyPanelManager'].values():
        survey_client_managers_size.setdefault(survey_panel_manager.SurveyClientId, 0)
        survey_client_managers_size[survey_panel_manager.SurveyClientId] += 1

    for survey_client in all_sfobjects['SurveyClient'].values():
        customer_survey = all_sfobjects['CustomerSurvey'].get(survey_client.CustomerSurveyId)
        if not customer_survey or customer_survey.CustomerId is None or customer_survey.AccountUpdatesEndDate is None or customer_survey.LiveSurveyStartDate is None or customer_survey.LiveSurveyEndDate is None:
            continue

        if customer_survey.LiveSurveyStartDate <= datetime.date.today() <= customer_survey.LiveSurveyEndDate:
            if survey_client_panel_size.get(survey_client.Id, 0) == 0:
                add_message('SurveyClient', 'SurveyClient-EmptySurveyClient', 'warning',
                            _std_fields(all_sfobjects, survey_client))

        if customer_survey.AccountUpdatesEndDate <= datetime.date.today() <= customer_survey.LiveSurveyEndDate:
            if survey_client_managers_size.get(survey_client.Id, 0) == 0:
                add_message('SurveyClient', 'SurveyClient-NoPanelManagers', 'warning',
                            _std_fields(all_sfobjects, survey_client))


def write_to_postgres():
    conn = psycopg2.connect(settings.monitordb_salesforce_url)

    # clear out any old data
    with conn.cursor() as cursor:
        sql = """
delete from validation_issues
"""
        cursor.execute(sql)
        conn.commit()

    sfimport.write_record_batch(conn, 'public.validation_issues', validation_issues)


def main(all_sfobjects=None):
    if all_sfobjects is None:
        all_sfobjects = sfimport.load_sf_from_latest_dump()
    find_account_record_types(all_sfobjects)
    root_accounts, account_forest_by_id, account_forest_by_name = sfimport.generate_account_forest(all_accounts=all_sfobjects['Account'].values())

    # basic object validation of just the objects by themselves
    check_contacts_are_correct(all_sfobjects)
    check_markets_are_correct(all_sfobjects)
    check_accounts_are_correct(all_sfobjects)
    check_divisions_are_correct(all_sfobjects)
    check_customerclientrelationships_are_correct(all_sfobjects)
    check_teams_are_correct(all_sfobjects, account_forest_by_id)
    check_accountgroups_are_correct(all_sfobjects)
    check_accountcontactrelations_are_correct(all_sfobjects)
    check_surveyrounds_are_correct(all_sfobjects)
    check_customersurveyrounds_are_correct(all_sfobjects)
    check_customersurveys_are_correct(all_sfobjects)
    check_surveyaccountmanagers_are_correct(all_sfobjects)
    check_surveyclients_are_correct(all_sfobjects)
    check_surveypanelmanagers_are_correct(all_sfobjects)
    check_surveypanelmembers_are_correct(all_sfobjects)
    check_surveyresponses_are_correct(all_sfobjects)
    check_contact_report_views_are_correct(all_sfobjects)
    check_contact_key_accounts_are_correct(all_sfobjects)
    check_contact_key_markets_are_correct(all_sfobjects)
    check_contact_key_customers_are_correct(all_sfobjects)
    check_contact_key_teams_are_correct(all_sfobjects)
    check_contact_reporting_categories_are_correct(all_sfobjects)
    # FIXME: validate SurveyTemplateDefinition?

    # more complex cross-object validations
    check_for_duplicate_business_keys(all_sfobjects)
    check_contact_survey_roles(all_sfobjects)
    check_accountcontactrelations_are_present(all_sfobjects)
    check_customerclientrelationships_are_present(all_sfobjects)
    check_email_domain_record_type_consistency(all_sfobjects)
    check_email_domain_top_level_account_consistency(all_sfobjects)
    check_account_hierarchy(all_sfobjects, root_accounts, account_forest_by_id)
    check_empty_customersurveys(all_sfobjects)
    check_empty_surveyclients(all_sfobjects)
    check_customersurvey_dates_are_sane(all_sfobjects)
    check_customersurveyround_dates_are_sane(all_sfobjects)

    write_to_postgres()


if __name__ == "__main__":
    main()
