import json
import base64
from aws_encryption_sdk import EncryptionSDKClient, CommitmentPolicy, StrictAwsKmsMasterKeyProvider
from pydantic import BaseModel
from cryptography.fernet import Fernet
from lib.settings import settings
from lib.sqsapi import SQS
from lib.portaldbapi import DocumentDB


DEFAULT_SENDER = '<EMAIL>' # TODO: move to secrets


class PendingCustomerContactService():
    def __init__(self, db):
        self.db = db

    def get_by_internal_id(self, internal_id: str):
        return bool(self.db.pendingcustomercontacts.find_one({'internal_id': internal_id, 'sfsync_date': None}))

class EmailServiceConfig(BaseModel):
    enabled: bool = False
    restricted_domains: list[str] = []
    templates: dict[str, str] = {}

class EmailService():
    def __init__(self, db: DocumentDB, sqs: SQS):
        self.db: DocumentDB = db
        self.sqs: sqs = sqs

    def send(self, sender: str, recipient: str, template: str, data: dict, metadata: dict) -> dict:
        success: bool = False
        es: EmailServiceConfig = EmailServiceConfig(**self.db.config.find_one({"_id": "email"}))
        template_id: str = es.templates.get(template)

        if not template_id:
            raise Exception(f"Template {template} does not exist")

        try:
            self.sqs.send_message(
                queue=settings.send_email_to_sg_queue_url,
                message_body=f'Email template {template_id} to {recipient}',
                message_attributes={
                    'fromEmail': {
                        'DataType': 'String',
                        'StringValue': sender,
                    },
                     'toEmail': {
                        'DataType': 'String',
                        'StringValue': recipient,
                    },
                     'templateId': {
                        'DataType': 'String',
                        'StringValue': template_id,
                    },
                     'templateData': {
                        'DataType': 'String',
                        'StringValue': json.dumps(data),
                    },
                    'customArgs': {
                        'DataType': 'String',
                        'StringValue': json.dumps(metadata),
                    },
                },
            )

            success: bool = True
        except Exception as e:
            raise ValueError(repr(e))

        return {
            'success': success,
        }

def main(event, context):
    db: DocumentDB = DocumentDB()
    sqs: SQS = SQS()
    es: EmailService = EmailService(db, sqs)
    pc: PendingCustomerContactService = PendingCustomerContactService(db)
    ec = EncryptionSDKClient(commitment_policy=CommitmentPolicy.REQUIRE_ENCRYPT_ALLOW_DECRYPT)
    kms_kwargs = dict(key_ids=[settings.cognito_key_ids_arn])
    master_key_provider = StrictAwsKmsMasterKeyProvider(**kms_kwargs)
    email: str = event['request']['userAttributes']['email']
    sub: str = event['request']['userAttributes']['sub']
    code = event['request']['code']
    plain_text_code = None
    fernet = Fernet(settings.confirmation_code_encryption_key)

    # decrypt the confirmation code
    if code:
        ciphertext = base64.b64decode(code)
        plain_text, _ = ec.decrypt(source=ciphertext, key_provider=master_key_provider)
        plain_text_code = plain_text.decode('utf-8')

    # if no confirmation code found, raise an error
    if not plain_text_code:
        raise ValueError('Confirmation code not found')

    # Construct the payload for the appropiate trigger source and send the email
    if event['triggerSource'] in ['CustomEmailSender_SignUp', 'CustomEmailSender_ResendCode']:
        # if user is still pending approval, do not send confirmation email
        if pc.get_by_internal_id(sub):
            return

        params = f'code={plain_text_code}&email={email}'
        encrypted_params: bytes = fernet.encrypt(params.encode()).decode()
        link: str = f'{settings.verify_login_link}?c={encrypted_params}'
        template: str = 'verify_login'
        template_data: dict[str, str] = {
            'link': link,
        }
        custom_args: dict[str, str] = {
            'cognito_event': event['triggerSource'],
        }
        es.send(DEFAULT_SENDER, email, template, template_data, custom_args)
    elif event['triggerSource'] == 'CustomEmailSender_ForgotPassword':
        params: str = f'code={plain_text_code}'
        encrypted_params: bytes = fernet.encrypt(params.encode()).decode()
        link: str = f'{settings.forgotten_password_link}?c={encrypted_params}'
        template: str = 'forgotten_password'
        template_data = {
            'link': link,
        }
        custom_args: dict[str, str] = {
            'cognito_event': event['triggerSource'],
        }
        es.send(DEFAULT_SENDER, email, template, template_data, custom_args)
    else:
        print('Unsupported triggerSource: ' + event['triggerSource'])


def lambda_handler(event, context):
    main(event, context)

if __name__ == '__main__':
    event = {}
    context = {}
    main(event, context)