import copy
import unittest
from unittest.mock import patch, MagicMock
import agents.email_scheduler_panel_users as email_scheduler_panel_users


class TestEmailSchedulerPanelUsers(unittest.TestCase):
    def setUp(self):
        self.base_survey_panel_member = {
            'Id': 'TESTID',
            'Name': '<EMAIL>',
            'SurveyJsId__c': 'SJS_ID',
            'Contact__r.Id': 'TESTCONTACTID',
            'Contact__r.Email': '<EMAIL>',
            'Contact__r.Name': 'Test User',
            'Contact__r.FirstName': 'Test',
            'Contact__r.LastName': 'User',
            'Contact__r.Language__c': '',
            'Contact__r.Location__r.Timezone__c': '',
            'Survey_Email_Triggered__c': 'false',
            'Survey_Email_Reminder_1__c': 'false',
            'Survey_Email_Reminder_2__c': 'false',
            'Survey_Email_Reminder_3__c': 'false',
            'Survey_Type__c': 'Barometer',
            'Survey_Client__r.Id': 'SC_ID',
            'Survey_Client__r.Survey_Name__c': 'TestSurvey Name',
            'Survey_Client__r.Signatory__r.Id': 'SIGNATORY_ID',
            'Survey_Client__r.Signatory__r.Name': 'Signatory Name',
            'Survey_Client__r.Signatory__r.Email': '<EMAIL>',
            'Survey_Client__r.Signatory__r.Title': '',
            'Survey_Client__r.Signatory__r.Account.Name': 'Test Account',
            'Survey_Client__r.Signatory__r.Banner__c': '',
            'Survey_Client__r.Signatory__r.Signature__c': '<p><img src="https://clientrelationship.file.force.com/servlet/rtaImage?eid=003WT000009VMqy&amp;feoid=00NWT000000puY1&amp;refid=0EMWT00000Ar8zV" alt="Screenshot 2025-06-05 at 12.35.08\u202fPM.png"></img></p><p><strong style="font-family: sans-serif;">Peter Bunarek</strong></p><p><span style="font-family: sans-serif;">President &amp; CEO</span></p><p><span style="font-family: sans-serif;">BBDO Atlanta</span></p><p><span style="font-family: sans-serif;">M. 404.316.3081</span></p>',
            'Survey_Client__r.Customers_Client__c': 'CUST_CLIENT_ID',
            'Survey_Client__r.Customer_Survey__r.Id': 'CUST_SURVEY_ID',
            'Survey_Client__r.Customer_Survey__r.External_Communication_Email_Address__c': '<EMAIL>',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_End_Date__c': '2025-06-22',
            'Survey_Client__r.Customer_Survey__r.Customer__r.RecordTypeId': 'REC_TYPE_ID',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Market__r.Timezone__c': 'America/New_York',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Id': 'CUST_PARENT_ID',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Name': 'Cust Parent Name',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Id': 'CUST_GRANDPARENT_ID',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Parent.Parent.Name': 'Cust Grandparent Name',
            'Survey_Client__r.Customer_Survey__r.Customer__r.Calculated_Organisation_Level__c': 'Level 7',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Id': 'CSR_ID',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_First_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Second_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Third_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Email_Live_Survey_Fourth_Request__c': '',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Send_Emails_Via_SES__c': 'false',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Account__r.Name': 'Test SR Account Name',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Id': 'SR_ID',
            'Survey_Client__r.Customer_Survey__r.Customer_Survey_Round__r.Survey_Round__r.Send_Emails_Via_SES__c': 'false',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_First_Request__c': '2025-06-04',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Second_Request__c': '2025-06-11',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Third_Request__c': '2025-06-16',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Fourth_Request__c': '2025-06-18',
            'Survey_Client__r.Customer_Survey__r.Live_Survey_Start_Date__c': '2025-06-04',
        }
        self.account_record_types = {
            "CRC's Customer - Advertising": MagicMock(spec=['Id']),
        }
        self.account_record_types["CRC's Customer - Advertising"].Id = 'REC_TYPE_ID'
        self.expected_result = dict(
        )


    def get_panel_members(self, count=1, pms_per_contact=1):
        panel_members = []
        for idx in range(count):
            panel_member = copy.deepcopy(self.base_survey_panel_member)
            panel_member['Id'] = f'TESTID{idx}'
            panel_member['Name'] = ''
            panel_members.append(panel_member)
        return panel_members


    def test_process_participants_for_ses(self):
        panel_members = self.get_panel_members(1, 1)
        contact_profile_count = {}
        contact_survey_names = {}
        scheduled_emails = {}
        result = email_scheduler_panel_users.process_participants('CSR_ID',
                                                                    panel_members, 
                                                                    contact_profile_count,
                                                                    contact_survey_names,
                                                                    0,
                                                                    self.account_record_types,
                                                                    {}
                                                                )
    
        self.assertEqual(len(result), 1)
        # TODO check fields in result
