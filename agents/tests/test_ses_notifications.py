import copy
import unittest
from unittest.mock import patch, MagicMock
import agents.ses_notifications as ses_notifications

class TestSesNotifications(unittest.TestCase):
    def setUp(self):
        self.base_message = {
            'eventType': '',
            'mail': {
                'tags': {
                    'tracked_template_name': ['live_survey_participant_dev'],
                    'survey_panel_member_id': ['test_pm_id'],
                },
                'destination': ['<EMAIL>'],
                'messageId': '12345',
            }
        }
        self.expected_result = dict(
            email='<EMAIL>',
            event=None,
            timestamp=None,
            ses_message_id='12345',
            survey_panel_member_id='test_pm_id',
            template='live_survey_participant_dev',
            sg_machine_open=False,
        )


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_bounce_permanent(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'Bounce'
        message['bounce'] = {
            'bounceType': 'Permanent',
            'bounceSubType': 'General',
        }
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'live_survey_participant_dev'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['processed'], 1)
        mock_db_instance.sesevents.bulk_write.assert_called_once()
        call_args, _ = mock_db_instance.sesevents.bulk_write.call_args
        self.assertEqual(len(call_args[0]), 1)
        inserted_doc = call_args[0][0]._doc
        # ignore timestamp for comparison
        inserted_doc['timestamp'] = None
        self.expected_result['event'] = 'bounce'
        self.expected_result['bounce_reason'] = 'General'
        self.assertDictEqual(inserted_doc, self.expected_result)


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_bounce_transient(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'Bounce'
        message['bounce'] = {
            'bounceType': 'Transient',
            'bounceSubType': 'General',
        }
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'live_survey_participant_dev'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['untracked_event'], 1)
        mock_db_instance.sesevents.bulk_write.assert_not_called()


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_delivery(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'Delivery'
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'live_survey_participant_dev'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['processed'], 1)
        mock_db_instance.sesevents.bulk_write.assert_called_once()
        call_args, _ = mock_db_instance.sesevents.bulk_write.call_args
        self.assertEqual(len(call_args[0]), 1)
        inserted_doc = call_args[0][0]._doc
        # ignore timestamp for comparison
        inserted_doc['timestamp'] = None
        self.expected_result['event'] = 'delivered'
        self.assertDictEqual(inserted_doc, self.expected_result)


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_complaint(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'Complaint'
        message['complaint'] = {
            'complaintFeedbackType': 'abuse',
        }
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'live_survey_participant_dev'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['processed'], 1)
        mock_db_instance.sesevents.bulk_write.assert_called_once()
        call_args, _ = mock_db_instance.sesevents.bulk_write.call_args
        self.assertEqual(len(call_args[0]), 1)
        inserted_doc = call_args[0][0]._doc
        # ignore timestamp for comparison
        inserted_doc['timestamp'] = None
        self.expected_result['event'] = 'bounce'
        self.expected_result['complaint_feedback'] = 'abuse'
        self.assertDictEqual(inserted_doc, self.expected_result)


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_untracked_event(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'SubscriptionConfirmation'
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'live_survey_participant_dev'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['untracked_event'], 1)
        mock_db_instance.sesevents.bulk_write.assert_not_called()


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_untracked_template(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'Delivery'
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'untracked_template'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['untracked_template'], 1)
        mock_db_instance.sesevents.bulk_write.assert_not_called()


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_no_template(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'Delivery'
        message['mail']['tags']['tracked_template_name'] = []
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'live_survey_participant_dev'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['template_null'], 1)
        mock_db_instance.sesevents.bulk_write.assert_not_called()


    @patch('agents.ses_notifications.portaldbapi.DocumentDB')
    @patch('agents.ses_notifications.settings')
    def test_no_panel_member(self, mock_settings, mock_db):
        message = copy.deepcopy(self.base_message)
        message['eventType'] = 'Delivery'
        message['mail']['tags']['survey_panel_member_id'] = []
        messages = [message]
        mock_settings.TRACKED_TEMPLATES = 'live_survey_participant_dev'
        mock_db_instance = mock_db.return_value
        result = ses_notifications.main(messages)
        self.assertEqual(result['no_panel_member_id'], 1)
        mock_db_instance.sesevents.bulk_write.assert_not_called()
