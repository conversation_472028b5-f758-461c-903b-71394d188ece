import copy
from datetime import datetime
import unittest
from unittest.mock import call, patch, MagicMock
import agents.email_sender_panel_users as email_sender_panel_users


class TestEmailSenderPanelUsers(unittest.TestCase):
    def setUp(self):
        self.base_email = {
            '_id': 'test_id',
            'survey_panel_member_ids': ['test_pm_id'],
            'sender_email': '<EMAIL>',
            'sender_name': '<PERSON> Bloggs',
            'recipient': '<EMAIL>',
            'template_name': 'test_template_name',
            'template_data': {},
            'template_metadata': {},
            'scheduled_date_utc': datetime(2025, 3, 18, 12, 14, 0).replace(tzinfo=None),
            'language': 'en',
            'vertical': 'adv',
            'banner_attachment': None,
            'signature_attachment': None,
            'sf_spm_field': 'test_field',
            'email_service_provider': 'ses',
        }


    @patch('agents.email_sender_panel_users.cache_sendgrid_template')
    @patch('agents.email_sender_panel_users.get_scheduled_emails_from_portaldb')
    @patch('agents.email_sender_panel_users.get_customer_survey_rounds_from_sf')
    @patch('agents.email_sender_panel_users.SendGridAPIClient')
    @patch('agents.email_sender_panel_users.emailapi.EmailServiceConfig')
    @patch('agents.email_sender_panel_users.emailapi.EmailService')
    @patch('agents.email_sender_panel_users.sqsapi.SQS')
    @patch('agents.email_sender_panel_users.sfapi.get_sf_connection')
    @patch('agents.email_sender_panel_users.portaldbapi.DocumentDB')
    @patch('agents.email_sender_panel_users.settings')
    def test_send_via_ses(self, mock_settings, mock_db, mock_sf, mock_sqs, mock_email_service, mock_esc, mock_sg, mock_get_csr, mock_get_emails, mock_cache):
        mock_db_instance = mock_db.return_value
        mock_email_service_instance = mock_email_service.return_value
        mock_collection = MagicMock()
        mock_db_instance.get_sf_collection.return_value = mock_collection
        mock_get_csr.return_value = {}
        mock_get_emails.return_value = {
            'CSR_ID': [
                copy.deepcopy(self.base_email),
                copy.deepcopy(self.base_email),
                copy.deepcopy(self.base_email),
            ]
        }
        email_sender_panel_users.main(None, None, None, None, True)

        mock_cache.assert_called()
        mock_email_service_instance.send.assert_called()
        send_at = datetime(2025, 3, 18, 12, 14, 0).replace(tzinfo=None)
        portaldb_record_map:str = 'scheduledemails-test_id'
        calls = [
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map=portaldb_record_map, service_provider='ses', ses_delay_offset=0),   
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map=portaldb_record_map, service_provider='ses', ses_delay_offset=1),   
            call('<EMAIL>', '<EMAIL>', 'test_template_name', {}, {}, send_at=send_at, from_name='Joe Bloggs', language='en', vertical='adv', banner_attachment=None, signature_attachment=None, portaldb_record_map=portaldb_record_map, service_provider='ses', ses_delay_offset=2),   
        ]
        mock_email_service_instance.send.assert_has_calls(calls)

