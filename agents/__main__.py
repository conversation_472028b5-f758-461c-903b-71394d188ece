import pulumi
import pulumi_aws as aws
import os
from libdeploy import deploy_function, deploy_sqs_function, stack, eventbridge_schedule, config, \
                        aws_region, aws_account_id, add_sns_lambda_target, add_sns_lambda_alert, deploy_sqs_sns_function

def deploy():
    stack_ref = pulumi.StackReference(f"organization/crc-backend/{stack}")
    secrets_arn = stack_ref.require_output('secrets_arn')
    secrets_name = stack_ref.require_output('secrets_name')
    agents_ecr_repo_url = stack_ref.require_output('agents_ecr_repo_url')
    agents_ecr_repo_name = stack_ref.require_output('agents_ecr_repo_name')
    default_sg_id = stack_ref.require_output('default_sg_id')
    lambda_subnet_ids = [stack_ref.require_output('private_subnet_a_id')]
    user_pool_kms_key_arn = stack_ref.require_output('user_pool_kms_key_arn')
    user_pool_arn = stack_ref.require_output('user_pool_arn')
    user_pool_id = user_pool_arn.apply(lambda x: x.split('/')[-1])
    monitoring_sns_topic_arn = stack_ref.require_output('monitoring_topic_arn')
    lambda_security_group_ids = [default_sg_id]

    # get the hash of the tagged image in ECR
    ecr_image = aws.ecr.get_image(repository_name=agents_ecr_repo_name, image_tag=stack)

    lambda_policy_extra = [
        {
            "Effect": "Allow",
            "Action": [
                "s3:ListBucket",
                "s3:PutObject",
                "s3:GetObject",
                "s3:CreateMultipartUpload",
            ],
            "Resource": [
                f"arn:aws:s3:::crc-sf-data-{stack}",
                f"arn:aws:s3:::crc-sf-data-{stack}/*",
                f"arn:aws:s3:::crc-csv-data-{stack}",
                f"arn:aws:s3:::crc-csv-data-{stack}/*",
                f"arn:aws:s3:::crc-banners-data-{stack}",
                f"arn:aws:s3:::crc-banners-data-{stack}/*",
                f"arn:aws:s3:::crc-signatures-data-{stack}",
                f"arn:aws:s3:::crc-signatures-data-{stack}/*",
                f"arn:aws:s3:::crc-srpreports-data-{stack}",
                f"arn:aws:s3:::crc-srpreports-data-{stack}/*",
                f"arn:aws:s3:::crc-sg-template-cache-{stack}/*",
                "arn:aws:s3:::business-suite-sf-csv-sync-upload-preprod",
                "arn:aws:s3:::business-suite-sf-csv-sync-upload-preprod/*",
                "arn:aws:s3:::business-suite-sf-csv-sync-upload-production",
                "arn:aws:s3:::business-suite-sf-csv-sync-upload-production/*",
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "sqs:SendMessage",
            ],
            "Resource": [
                f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-send_email_to_sg-{stack}-queue",
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "sqs:SendMessage",
            ],
            "Resource": [
                f"arn:aws:sqs:{aws_region}:{aws_account_id}:crc-agent-send_email_to_ses-{stack}-queue",
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "cognito-idp:ListUsers",
                "cognito-idp:AdminCreateUser",
                "cognito-idp:AdminDisableUser",
                "cognito-idp:AdminEnableUser",
                "cognito-idp:AdminSetUserPassword",
                "cognito-idp:AdminResetUserPassword",
                "cognito-idp:AdminUpdateUserAttributes",
            ],
            "Resource": [
                pulumi.Output.format("arn:aws:cognito-idp:{}:{}:userpool/{}", aws_region, aws_account_id, user_pool_id),
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "amplify:GenerateAccessLogs",
            ],
            "Resource": [
                f"arn:aws:amplify:{aws_region}:{aws_account_id}:apps/d33bamnor76dvz/accesslogs/*",
            ]
        }
    ]

    # create function to send slack warning
    warning_lambda_role, warning_lambda_function = deploy_function(f'crc-agent-slack_monitoring-{stack}',
                                                                   f'agents.sns_to_slack.lambda_handler',
                                                                   lambda_security_group_ids,
                                                                   lambda_subnet_ids,
                                                                   agents_ecr_repo_url,
                                                                   ecr_image.image_digest,
                                                                   secrets_arn,
                                                                   secrets_name)
    add_sns_lambda_target('slack_monitoring', warning_lambda_function.arn, monitoring_sns_topic_arn)

    # setup the lambdas
    SQS_LAMBDAS = [
        'send_email_to_sg',
        'send_email_to_ses',
        'sync_optout_to_sf',
    ]
    lambda_sqs_queues = {}
    for sqs_agent_name in SQS_LAMBDAS:
        lambda_function_name = f'crc-agent-{sqs_agent_name}-{stack}'
        iam_lambda_role, lambda_function, sqs_event_source, sqs_queue, sqs_dead_queue = deploy_sqs_function(lambda_function_name,
                                                                                                            f'agents.{sqs_agent_name}.lambda_handler',
                                                                                                            lambda_security_group_ids,
                                                                                                            lambda_subnet_ids,
                                                                                                            agents_ecr_repo_url,
                                                                                                            ecr_image.image_digest,
                                                                                                            secrets_arn,
                                                                                                            secrets_name,
                                                                                                            lambda_function_memory_size_mb=2048,
                                                                                                            lambda_policy_extra=lambda_policy_extra)
        lambda_sqs_queues[sqs_agent_name] = sqs_queue
        add_sns_lambda_alert(lambda_function_name, monitoring_sns_topic_arn)

    # setup the lambda to receive SES delivery notifications
    for sqs_agent_name in ['ses_notifications']:
        lambda_function_name = f'crc-agent-{sqs_agent_name}-{stack}'
        iam_lambda_role, lambda_function, sqs_event_source, sqs_queue, sqs_dead_queue = deploy_sqs_sns_function(lambda_function_name,
                                                                                                                f'agents.{sqs_agent_name}.lambda_handler',
                                                                                                                lambda_security_group_ids,
                                                                                                                lambda_subnet_ids,
                                                                                                                agents_ecr_repo_url,
                                                                                                                ecr_image.image_digest,
                                                                                                                secrets_arn,
                                                                                                                secrets_name,
                                                                                                                lambda_function_memory_size_mb=2048,
                                                                                                                lambda_policy_extra=lambda_policy_extra)
        lambda_sqs_queues[sqs_agent_name] = sqs_queue
        add_sns_lambda_alert(lambda_function_name, monitoring_sns_topic_arn)

    SCHEDULED_LAMBDAS = [
        ('survey_setup', 'cron(0 * * * ? *)'),
        ('email_scheduler_staff_users', 'cron(0 9-23 * * ? *)'),
        #('email_scheduler_panel_users', 'cron(0 9 * * ? *)'), #NOTE: leaving old schedule for now for easy rollback
        ('email_scheduler_panel_users', 'cron(0 * * * ? *)'),
        ('email_sender_panel_users', 'cron(0/15 * * * ? *)'),
        ('sync_confirmaccounts_to_sf', 'cron(0 * * * ? *)'),
        ('sync_confirmpanel_to_sf', 'cron(0 * * * ? *)'),
        ('sync_sf_to_s3', 'cron(0 * * * ? *)'),
        ('sync_sfschema_to_s3', 'rate(6 hours)'),
        ('sync_sfsimple_to_portaldb', 'cron(0 * * * ? *)'),
        ('sync_surveyresponses_to_sf', 'cron(0 * * * ? *)'),
        ('sync_reports_to_portaldb', 'rate(3 hours)'),
        ('sync_email_analytics_to_sf', 'rate(10 minutes)'),
        ('sync_responses_to_portaldb', 'rate(3 hours)'),
        ('sync_customersurveyrounds_to_portaldb', 'rate(1 hours)'),
        ('sync_customercontacts_to_portaldb', 'rate(1 hours)'),
        ('sync_pendingcustomercontacts_to_sf', 'rate(1 hours)'),
        ('sync_panel_member_status_to_portaldb', 'rate(1 hours)'),
        ('sync_accesslog_to_sf', 'rate(1 hours)'),
        ('sync_confirmaccounts_to_portaldb', 'rate(1 hours)'),
        ('sync_confirmpanel_to_portaldb', 'rate(1 hours)'),
        ('sync_account_manager_activity_to_sf', 'rate(1 hours)'),
        ('sync_panel_manager_activity_to_sf', 'rate(1 hours)'),
        ('sync_sf_contacts_to_cognito', 'rate(1 hours)'),
        ('sync_finalaccounts_to_portaldb', 'rate(1 hours)'),
        ('sync_finalpanel_to_portaldb', 'rate(1 hours)'),
        ('sync_banners_to_s3', 'rate(1 hours)'),
        ('sync_signatures_to_s3', 'rate(1 hours)'),
        ('sync_markets_to_portaldb', 'cron(0 6 * * ? *)'),
        ('sync_srpreports_to_portaldb', 'rate(1 hours)'),
        ('sync_confirmaccounts_audit_to_sf', 'rate(1 hours)'),
        ('sync_confirmpanel_audit_to_sf', 'rate(1 hours)'),
        ('sync_survey_access_audit_to_sf', 'rate(1 hours)'),
        ('sync_survey_updates_to_portaldb', 'rate(3 hours)'),
        ('sync_customerclientaccounts_to_portaldb', 'cron(0 * * * ? *)'),
        ('confirmaccounts_new_objects_daily_state', 'cron(0 6 * * ? *)'),
    ]
    for lambda_agent_name, schedule_expression in SCHEDULED_LAMBDAS:
        lambda_function_name = f'crc-agent-{lambda_agent_name}-{stack}'

        handler_module = f'agents.{lambda_agent_name}.lambda_handler'
        lambda_role, lambda_function = deploy_function(lambda_function_name,
                                                       handler_module,
                                                       lambda_security_group_ids,
                                                       lambda_subnet_ids,
                                                       agents_ecr_repo_url,
                                                       ecr_image.image_digest,
                                                       secrets_arn,
                                                       secrets_name,
                                                       lambda_function_memory_size_mb=2048,
                                                       policy_extra=lambda_policy_extra,
                                                       lambda_function_timeout_secs=15*60,
                                                       async_max_retry_attempts=0)
        iam_eventbridge_role, eventbridge_lambda_schedule = eventbridge_schedule(lambda_function_name, schedule_expression, config.require("eventbridge_schedule_state"))
        add_sns_lambda_alert(lambda_function_name, monitoring_sns_topic_arn)

    cognito_email_lambda_policy_extra = [
        {
            "Effect": "Allow",
            "Action": [
                "sqs:SendMessage",
            ],
            "Resource": [
                lambda_sqs_queues['send_email_to_sg'].arn,
            ]
        },
        {
            "Effect": "Allow",
            "Action": [
                "kms:Decrypt",
            ],
            "Resource": [
                user_pool_kms_key_arn
            ]
        },
    ]

    # The cognito email trigger lambda thing
    iam_lambda_role, cognito_email_lambda = deploy_function(f'crc-agent-cognito_email_trigger-{stack}',
                                           'agents.cognito_email_trigger.lambda_handler',
                                           lambda_security_group_ids,
                                           lambda_subnet_ids,
                                           agents_ecr_repo_url,
                                           ecr_image.image_digest,
                                           secrets_arn,
                                           secrets_name,
                                           lambda_function_timeout_secs=60,
                                           policy_extra=cognito_email_lambda_policy_extra)
    add_sns_lambda_alert(f'crc-agent-cognito_email_trigger-{stack}', monitoring_sns_topic_arn)

    # allow cognito to call the email function
    cognito_lambda_permission = aws.lambda_.Permission(f"cognito-lambda-cognito_email_trigger-{aws_region}-{stack}",
                                                       action="lambda:InvokeFunction",
                                                       function=cognito_email_lambda.arn,
                                                       principal="cognito-idp.amazonaws.com")


if __name__ == '__main__':
    deploy()
