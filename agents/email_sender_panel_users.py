""" Send panel member survey emails for live survey rounds

DATA:
    FROM: PortalDB (scheduledemails)
    TO: Sendgrid
        PortalDB (scheduledemails)
        SalesForce (Survey_Panel_Member__c)

"""
import argparse
import boto3
import json
import requests
from itertools import islice
from bson import ObjectId
from datetime import datetime, timedelta, UTC
from io import BytesIO
from pymongo import UpdateOne
from sendgrid import SendGridAPIClient
from simple_salesforce import Salesforce, format_soql
from typing import Optional
from lib.settings import settings
from lib import emailapi, sfapi, portaldbapi, sqsapi


SF_BATCH_SIZE = 1000


def boolify(value):
    return value in {'true', 'True', 'TRUE', '1', 1, True}


def batch_iterable(iterable, batch_size):
    iterable = iter(iterable)
    while True:
        batch = list(islice(iterable, batch_size))
        if not batch:
            break
        yield batch
    

def cache_sendgrid_template(esc: emailapi.EmailServiceConfig, sg, template_name: Optional[str], vertical: Optional[str], language: Optional[str], cache_tracker:set[str]) -> None:
    """ Fetches the template from SendGrid and caches it in S3 for later use by the send_email_to_ses agent
    Args:
        esc (emailapi.EmailServiceConfig): Email service config
        sg (SendGridAPIClient): SendGrid API client
        template_name (str): Template name
        vertical (str): Vertical name
        language (str): Language code
        cache_tracker (set): Set of cached template ids
    """
    # get template id
    if vertical:
        template_name = f"{vertical.lower()}:{template_name}"
    if language:
        template_name = f"{template_name}:{language.lower()}"
    template_id = esc.templates.get(template_name)
    if not template_id:
        raise Exception(f"Template {template_name} does not exist")

    if template_id in cache_tracker:
        # template already cached
        return
    
    response = requests.get(f'{sg.host}/v3/templates/{template_id}', headers={'Authorization': f'Bearer {sg.api_key}'})
    response.raise_for_status()
    template = response.json()
    active_version = [x for x in template['versions'] if x['active']][0]

    print(f'   >> CACHING SENDGRID TEMPLATE: {template_name} ({template_id})')
    # upload the template to S3
    s3 = boto3.client('s3')
    s3_key = f'{template_id}.json'
    json_string = json.dumps(active_version)
    s3.put_object(
        Bucket=settings.SG_TEMPLATE_CACHE_BUCKET,
        Key=s3_key,
        Body=json_string,
        ContentType='application/json'
    )
    cache_tracker.add(template_id)


def get_customer_survey_rounds_from_sf(sf:Salesforce, customer_survey_round_id:str|None = None) -> dict:
    print(' >> FETCHING: CUSTOMER SURVEY ROUNDS FROM SF...')
    customer_survey_rounds:dict[str, bool] = {}

    soql = """
    SELECT Id,
           Disable_External_Communication__c,
           Survey_Round__r.Disable_External_Communication__c
    FROM  Customer_Survey_Round__c
    WHERE Current_Round__c = True
    """
    if customer_survey_round_id:
        soql = soql + " AND Id = {customer_survey_round_id}"
    
    query:str = format_soql(soql, customer_survey_round_id=customer_survey_round_id)

    for csr in sfapi.bulk_query(sf, query):
        external_comms_disabled = boolify(csr.get('Disable_External_Communication__c')) or boolify(csr.get('Survey_Round__r.Disable_External_Communication__c'))
        customer_survey_rounds[csr.get('Id')] = external_comms_disabled
    
    return customer_survey_rounds


def get_scheduled_emails_from_portaldb(collection:portaldbapi.DocumentDB,
                                       customer_survey_round_id:str|None = None,
                                       customer_survey_id:str|None = None,
                                       contact_id:str|None = None,
                                       survey_panel_member_id:str|None = None,) -> list[dict]:
    print(' >> FETCHING: SCHEDULED EMAILS FROM PORTALDB...')
    scheduled_emails:dict[str, list] = {}

    now = datetime.now(UTC)  
    start_time = now - timedelta(hours=1)  
    end_time = now + timedelta(minutes=15)

    query:dict = {
        'type': 'panel',
        "scheduled_date_utc": {
            "$gte": start_time,
            "$lt": end_time
        },
        "sent_to_sendgrid_date": {"$exists": False},
        'deleted': {'$exists': False},
    }
    projection:dict = {
        'customer_survey_round_id': 1,
        'survey_panel_member_id': 1,
        'survey_panel_member_ids': 1,
        'sender_email': 1,
        'sender_name': 1,
        'recipient': 1,
        'template_name': 1,
        'template_data': 1,
        'template_metadata': 1,
        'scheduled_date_utc': 1,
        'language': 1,
        'vertical': 1,
        'banner_attachment': 1,
        'signature_attachment': 1,
        'sf_spm_field': 1,
        'email_service_provider': 1,
    }

    # attach any additional filters
    if customer_survey_round_id:
        query['customer_survey_round_id'] = customer_survey_round_id
    if customer_survey_id:
        query['customer_survey_id'] = customer_survey_id
    if contact_id:
        query['contact_id'] = contact_id
    if survey_panel_member_id:
        query['survey_panel_member_id'] = survey_panel_member_id

    for doc in collection.find(query, projection):
        csr_id = doc.get('customer_survey_round_id')
        scheduled_emails.setdefault(csr_id, []).append(doc)

    return scheduled_emails


def main(customer_survey_round_id:str|None = None,
         customer_survey_id:str|None = None,
         contact_id:str|None = None,
         survey_panel_member_id:str|None = None,
         writeit:bool = False):
    print('START: EMAIL SENDER PANEL USERS')

    sf:Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db:portaldbapi.DocumentDB = portaldbapi.DocumentDB()
    collection:portaldbapi.DocumentDB = portaldbapi.get_sf_collection(db, portaldbapi.PORTALDB_SCHEDULED_EMAILS_COLLECTION)
    sqs:sqsapi.SQS = sqsapi.SQS()
    es:emailapi.EmailService = emailapi.EmailService(db, sqs)
    esc: emailapi.EmailServiceConfig = emailapi.EmailServiceConfig(**db.config.find_one({"_id": "email"}))
    sg: SendGridAPIClient = SendGridAPIClient(settings.SENDGRID_API_KEY)

    batch_db:list[UpdateOne] = []
    batch_sf:list[dict] = []
    failed:set = set()
    today:datetime.date = datetime.now(UTC)
    sendgrid_count:int = 0
    ses_count:int = 0

    # fail if incorrect parameter configuration is passed
    # only one of the parameters (apart writeit) can be passed, fail if more than one is passed
    if sum([customer_survey_round_id is not None,
            customer_survey_id is not None,
            contact_id is not None,
            survey_panel_member_id is not None]) > 1:
        raise Exception('Only one of the parameters (apart from writeit) can be passed')

    # get all customer survey rounds and there current external comms status
    customer_survey_rounds:dict[str, bool] = get_customer_survey_rounds_from_sf(sf, customer_survey_round_id)

    # get all emails scheduled to be sent today
    scheduled_emails:dict[str, list] = get_scheduled_emails_from_portaldb(collection,
                                                                          customer_survey_round_id,
                                                                          customer_survey_id,
                                                                          contact_id,
                                                                          survey_panel_member_id)

    cached_template_id_tracker:set[str] = set()
    for csr_id, scheduled_emails in scheduled_emails.items():
        print(f' # START: Customer Survey Round: {csr_id}')

        # skip if survey round or customer survey round have disabled comms
        # TODO: we should add a check for customer survey as well. It will be covered by schedules being marked as deleted, but no-harm is a further check
        if customer_survey_rounds.get(csr_id):
            print(f'   >> SKIPPED: Customer Survey Round: {csr_id} has comms disabled')
            continue

        email_index:int = 0
        for email in scheduled_emails:
            id:ObjectId = email.get('_id')
            spm_ids:list[str] = email.get('survey_panel_member_ids')
            sender_email:str = email.get('sender_email')
            sender_name:str = email.get('sender_name')
            recipient:str = email.get('recipient')
            email_template_name:str = email.get('template_name')
            template_data:dict = email.get('template_data')
            template_metadata:dict = email.get('template_metadata')
            send_at:datetime = email.get('scheduled_date_utc')
            language:str = email.get('language')
            vertical:str = email.get('vertical')
            banner_attachment:str = email.get('banner_attachment')
            signature_attachment:str = email.get('signature_attachment')
            sf_spm_field:str = email.get('sf_spm_field')
            portaldb_record_map:str = f'scheduledemails-{id}'
            email_service_provider:str = email.get('email_service_provider', 'sendgrid')
            ses_delay_offset:int = 0
            if email_service_provider == 'ses':
                # create and offset between 0 and 900 to avoid SES rate limit of 14 per second
                ses_delay_offset = email_index % 900
                email_index += 1
            # push email to SQS queue
            try:
                if writeit:
                    # if sending via SES, cache the email template prior to sending the email
                    if email_service_provider == 'ses':
                        cache_sendgrid_template(esc, sg, email_template_name, vertical, language, cached_template_id_tracker)

                    es.send(sender_email, 
                            recipient, 
                            email_template_name, 
                            template_data, 
                            template_metadata,
                            send_at=send_at,
                            from_name=sender_name,
                            language=language, 
                            vertical=vertical, 
                            banner_attachment=banner_attachment,
                            signature_attachment=signature_attachment,
                            portaldb_record_map=portaldb_record_map,
                            service_provider=email_service_provider,
                            ses_delay_offset=ses_delay_offset)
        
                # update the scheduled email record in the portaldb to mark as sent to SendGrid
                batch_db.append(UpdateOne({
                    '_id': id
                }, {
                    '$set': {
                        'sent_to_sendgrid_date': today
                    }
                }))

                # update all the associated SPM's in SF to flag the email has been scheduled
                for spm_id in spm_ids:
                    batch_sf.append({
                        'Id': spm_id,
                        sf_spm_field: True
                    })

                if email_service_provider and email_service_provider == 'ses':
                    ses_count += 1
                else:
                    sendgrid_count += 1

                print(f'   :: Email Scheduled: scheduledemails record {id} for {recipient} at {send_at} UTC')
            except Exception as e:
                print(f"Failed to send email for record {id}: {e}")
                failed.add(str(id))
    
    if writeit and batch_db:
        collection.bulk_write(batch_db)
    
    if writeit and batch_sf:
        for batch in batch_iterable(batch_sf, SF_BATCH_SIZE):
            sfapi.bulk_update(sf, "Survey_Panel_Member__c", batch)

    print(f'END: EMAIL SENDER PANEL USERS: {sendgrid_count} Emails Sent to SendGrid, {ses_count} Emails Sent to SES')

    # if we have any failures, safely raise them here so an alert can be triggered
    if failed:
        raise Exception(f"Failed to schedule emails for records: {failed}")


def lambda_handler(event, context):
    main(
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument('--customer_survey_round_id', default=None, help='single customer survey round to run for')
    ap.add_argument('--customer_survey_id', default=None, help='single customer survey to run for')
    ap.add_argument('--contact_id', default=None, help='single contact to run for')
    ap.add_argument('--survey_panel_member_id', default=None, help='single survey panel member to run for')
    ap.add_argument('--writeit', action='store_true', help='Actually write data')
    args = ap.parse_args()

    main(
        customer_survey_round_id=args.customer_survey_round_id,
        customer_survey_id=args.customer_survey_id,
        contact_id=args.contact_id,
        survey_panel_member_id=args.survey_panel_member_id,
        writeit=args.writeit
    )
