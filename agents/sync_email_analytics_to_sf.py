""" Read sendgrid events from the portal DB and update the email status fields of Survey Panel Member records in Salesforce

DATA:
    FROM: portal DB (sendgridevents)
    TO: Salesforce (Survey_Panel_Member__c)
"""
import argparse
import datetime
from simple_salesforce import Salesforce
from typing import Union, Optional

from lib import sfapi
from lib.settings import settings
from lib.portaldbapi import DocumentDB


SG_EVENT_TO_SF_FIELD_MAP = {
    'delivered': {
        'live_survey_participant': 'Survey_Email_Delivered__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Delivered__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Delivered__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Delivered__c',
        'live_barometer_participant': 'Survey_Email_Delivered__c',
        'live_barometer_participant_reminder_1': 'Survey_Email_Reminder_1_Delivered__c',
        'live_barometer_participant_reminder_2': 'Survey_Email_Reminder_2_Delivered__c',
        'live_barometer_participant_reminder_3': 'Survey_Email_Reminder_3_Delivered__c',
    },
    'open': {
        'live_survey_participant': 'Survey_Email_Opened__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Opened__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Opened__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Opened__c',
        'live_barometer_participant': 'Survey_Email_Opened__c',
        'live_barometer_participant_reminder_1': 'Survey_Email_Reminder_1_Opened__c',
        'live_barometer_participant_reminder_2': 'Survey_Email_Reminder_2_Opened__c',
        'live_barometer_participant_reminder_3': 'Survey_Email_Reminder_3_Opened__c',
    },
    'click': {
        'live_survey_participant': 'Survey_Email_Clicked__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Clicked__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Clicked__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Clicked__c',
        'live_barometer_participant': 'Survey_Email_Clicked__c',
        'live_barometer_participant_reminder_1': 'Survey_Email_Reminder_1_Clicked__c',
        'live_barometer_participant_reminder_2': 'Survey_Email_Reminder_2_Clicked__c',
        'live_barometer_participant_reminder_3': 'Survey_Email_Reminder_3_Clicked__c',
    },
    'bounce': {
        'live_survey_participant': 'Survey_Email_Bounced__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Bounced__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Bounced__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Bounced__c',
        'live_barometer_participant': 'Survey_Email_Bounced__c',
        'live_barometer_participant_reminder_1': 'Survey_Email_Reminder_1_Bounced__c',
        'live_barometer_participant_reminder_2': 'Survey_Email_Reminder_2_Bounced__c',
        'live_barometer_participant_reminder_3': 'Survey_Email_Reminder_3_Bounced__c',
    },
    'drop': {
        'live_survey_participant': 'Survey_Email_Bounced__c',
        'live_survey_participant_reminder_1': 'Survey_Email_Reminder_1_Bounced__c',
        'live_survey_participant_reminder_2': 'Survey_Email_Reminder_2_Bounced__c',
        'live_survey_participant_reminder_3': 'Survey_Email_Reminder_3_Bounced__c',
        'live_barometer_participant': 'Survey_Email_Bounced__c',
        'live_barometer_participant_reminder_1': 'Survey_Email_Reminder_1_Bounced__c',
        'live_barometer_participant_reminder_2': 'Survey_Email_Reminder_2_Bounced__c',
        'live_barometer_participant_reminder_3': 'Survey_Email_Reminder_3_Bounced__c',
    },
}

def update_sf_survey_panel_member(sf: Salesforce, survey_panel_members_updates: dict[str, dict]) -> None:
    statuses = sfapi.bulk_update(sf, "Survey_Panel_Member__c", survey_panel_members_updates.values())
    for status in statuses:
        sfapi.detect_bulk2_errors(status)


def fetch_email_events(db: DocumentDB, starttime: Optional[datetime.datetime] = None, endtime: Optional[datetime.datetime] = None, survey_panel_member_id:Optional[str] = None) -> dict[str, dict]:
    events_by_panel_member: dict[str, dict] = {}
    # fetch the last xx mins email events from the DB
    filter_end_date: datetime = datetime.datetime.now(tz=datetime.UTC)
    filter_start_date: datetime = filter_end_date - datetime.timedelta(minutes=settings.SENDGRID_DATE_RANGE_OFFSET)
    # override time range if explicitly specified
    if starttime:
        filter_start_date = starttime
    if endtime:
        filter_end_date = endtime
    query = {
        "timestamp": {
            "$gte": filter_start_date,
            "$lte": filter_end_date
        }
    }
    # filter by survey panel member ID if specified
    if survey_panel_member_id:
        query['survey_panel_member_id'] = survey_panel_member_id
    # query sendgrid events first
    email_events = db.sendgridevents.find(query)
    for event in email_events:
        panel_member_id = event.get('survey_panel_member_id')
        event_list = events_by_panel_member.setdefault(panel_member_id, [])
        event_list.append(event)
    # query ses events next
    email_events = db.sesevents.find(query)
    for event in email_events:
        panel_member_id = event.get('survey_panel_member_id')
        event_list = events_by_panel_member.setdefault(panel_member_id, [])
        event_list.append(event)

    return events_by_panel_member


def main(survey_panel_member_id:Optional[str] = None, starttime: Optional[str] = None, endtime: Optional[str] = None, writeit:bool = False) -> None:
    sf: Salesforce = sfapi.get_sf_connection(settings.SF_INSTANCE, settings.SF_CLIENT_ID, settings.SF_CLIENT_SECRET)
    db: DocumentDB = DocumentDB()

    if (starttime and not endtime) or (endtime and not starttime):
        raise ValueError("Both starttime and endtime must be specified if either is provided.")

    if starttime and endtime:
        starttime = datetime.datetime.fromisoformat(starttime)
        endtime = datetime.datetime.fromisoformat(endtime)

    survey_panel_members_updates: dict = {}

    event_count = 0
    panel_member_email_events = fetch_email_events(db, starttime, endtime, survey_panel_member_id)
    # iterate over the emails and determine what events have occured
    for survey_panel_member_id, email_events in panel_member_email_events.items():
        # store the status for each event
        email_event_status: dict[str, Union[bool,str]] = dict()

        # iterate over the events on the email and update the initial/default email event status
        for event in email_events:
            # we've seen the event, so update the status
            event_name = event.get('event')
            event_template = event.get('template')
            event_timestamp = event.get('timestamp')
            if event_name == 'open' and event.get('sg_machine_open'):
                # the email was opened by a machine, skip it
                continue
            # if event fron a non-prod environment env, strip the env suffix
            for suffix in ['_dev', '_stage']:
                if event_template.endswith(suffix):
                    event_template = event_template[:-len(suffix)]
                    break
            email_event_status[SG_EVENT_TO_SF_FIELD_MAP[event_name][event_template]] = True
            event_count += 1

        survey_panel_members_updates[survey_panel_member_id] = email_event_status
        # attach the survey panel member ID to the update
        survey_panel_members_updates[survey_panel_member_id]['id'] = survey_panel_member_id

    # update each SF survey panel member with their email activity
    if survey_panel_members_updates and writeit:
        update_sf_survey_panel_member(sf, survey_panel_members_updates)

    print(f"Processed {event_count} email events for {len(survey_panel_members_updates.keys())} survey panel members")


def lambda_handler(event, context):
    main(
        survey_panel_member_id=None,
        starttime=None,
        endtime=None,
        writeit=True
    )


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("--survey_panel_member_id", type=str, help="The ID of the survey panel member to update")
    ap.add_argument("--starttime", type=str, help="The time to start looking for email events, e.g. 2011-11-04T00:05:23Z")
    ap.add_argument("--endtime", type=str, help="The time to stop looking for email events")
    ap.add_argument("--writeit", action="store_true", help="Actually write to the database")
    args = ap.parse_args()

    main(args.survey_panel_member_id, args.starttime, args.endtime, args.writeit)
