name: Deployment
run-name: Deploy ${{ github.event.inputs.component }} to ${{ github.event.inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to'
        required: true
        type: choice
        options:
          - dev
          - stage
          - prod
      component:
        description: 'Component to deploy'
        required: true
        type: choice
        options:
          - apis
          - agents
          - all

# Add permissions needed for OIDC
permissions:
  id-token: write
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.inputs.branch }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12.7'

      - name: Install Poetry
        uses: snok/install-poetry@v1
        with:
          version: 1.8.0

      - name: Install dependencies
        run: |
          poetry config virtualenvs.create false
          poetry install --no-interaction --no-ansi

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: arm64

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-region: ${{ secrets.AWS_REGION }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Build APIs
        if: ${{ github.event.inputs.component == 'apis' || github.event.inputs.component == 'all' }}
        run: ./build-apis ${{ github.event.inputs.environment }}

      - name: Deploy APIs
        if: ${{ github.event.inputs.component == 'apis' || github.event.inputs.component == 'all' }}
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: ${{ github.event.inputs.environment }}
          work-dir: ./api
          cloud-url: s3://crc-pulumi/crc
        env:
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}

      - name: Build Agents
        if: ${{ github.event.inputs.component == 'agents' || github.event.inputs.component == 'all' }}
        run: ./build-agents ${{ github.event.inputs.environment }}

      - name: Deploy Agents
        if: ${{ github.event.inputs.component == 'agents' || github.event.inputs.component == 'all' }}
        uses: pulumi/actions@v6
        with:
          command: up
          stack-name: ${{ github.event.inputs.environment }}
          work-dir: ./agents
          cloud-url: s3://crc-pulumi/crc
        env:
          PULUMI_CONFIG_PASSPHRASE: ${{ secrets.PULUMI_CONFIG_PASSPHRASE }}
