import re
import time
import asyncio
from datetime import datetime, date
from importlib import import_module
from litestar.exceptions import PermissionDeniedException
from lib.portaldbapi import DocumentDBAsync
from api.clientarea.src.dependencies import User
from api.clientarea.src.utils.cards.ExportResponse import ExportResponse
from api.clientarea.src.handlers.dashboard.get_dashboard_handler import get_response_data_required, build_response_query, get_round_lookup, get_response_projection


async def fetch_responses(collection, query, projection):
    records = []
    cursor = collection.find(query, projection).batch_size(2500)
    async for record in cursor:
        records.append(record)
    return records


class GetExportHandler:
    def __init__(self, dashboard, global_filters, local_filters, user: User, documentdb: DocumentDBAsync):
        self.dashboard_type = dashboard
        self.global_filters = global_filters
        self.local_filters = local_filters
        self.metadata = True
        self.user = user
        self.db = documentdb

        self.base_response_projection_fields = set(
            [
                'Id',
                # required by base Card implementation
                'is_extra_question',
            ]
        )
        self.card_config = {
            'filters': [
                'round_id', 'account_id', 'market_id', 'market_country_id', 'market_view_filter_id',
                'account_view_filter_id', 'contact_type', 'contact_division',
                'agencies', 'team_type', 'team_name', 'team_market_id', 'survey_name',
                'team_market_group', 'sector'
            ],
            'projection': {
                'agency_brand_name': 1,
                'account_name': 1,
                'contact_division': 1,
                'contact_id': 1,
                'contact_job_title': 1,
                'contact_name': 1,
                'contact_type': 1,
                'customer_survey_id': 1,
                'customer_survey_round_id': 1,
                'feedback': 1,
                'feedback_translated': 1,
                'feedback_question': 1,
                'feedback_question_en': 1,
                'is_extra_question': 1,
                'linked_trr_survey_id': 1,
                'market_country': 1,
                'market_name': 1,
                'office_name': 1,
                'rating': 1,
                'rating_question': 1,
                'rating_question_en': 1,
                'responded': 1,
                'response_date': 1,
                'survey_client_id': 1,
                'survey_name': 1,
                'survey_start_date': 1,
                'team_market_group': 1,
                'team_name': 1,
                'team_type': 1,
                'with_market_name': 1,
                # not fields on response, but generated by the export card
                'extra_question_1': 1,
                'extra_question_2': 1,
                'extra_question_3': 1,
                'extra_question_4': 1,
                'extra_question_5': 1,
            }
        }


    async def handler(self):
        start_time_all = time.time()
        accounts: list = self.user.account_hierarchy
        reporting_views: dict = self.user.reporting_views

        # FIXME: move this to a route guard
        if 'dashboards' not in self.user.features:
            raise PermissionDeniedException('User does not have correct features to access dashboards')

        # FIXME: move this to a route guard
        if not (self.user.roles.get('insights_viewer') or self.user.roles.get('panel_manager')):
            raise PermissionDeniedException('User does not have correct roles to access dashboards')

        # FIXME: move this to a route guard
        if not reporting_views:
            raise PermissionDeniedException('No reporting role found for user')

        # permission check to see if any of the account_id are in the accounts list
        if self.global_filters.get('account_id', []) and not set(self.global_filters['account_id']).issubset(set(accounts)):
            raise PermissionDeniedException('Account not in user account hierarchy')
        
        #FIXME: move to contact agent
        # generate user round index key
        round_index_key = f'{self.user.account_organisation_level}_round_index'
        round_index_key = round_index_key.lower()
        round_index_key = round_index_key.replace(' ', '_').replace('-', '_')
        round_index_key = re.sub(r'\s*\(.*?\)', '', round_index_key)

        # get the dashboard from the db, we need it to determine which type of responses to export
        dashboard_config = await self.db.dashboards.find_one({'type': self.dashboard_type}, {'cards': 1})
        if not dashboard_config:
            return {'responses': []}

        # get the type of response data this dashboard requires
        data_required = get_response_data_required(dashboard_config)
        barometer_dashboard = True if 'barometer' in data_required else False
        # if the dashboard shows barometer data, check the user has permissions to view it
        if 'barometer' in data_required and 'dashboards_barometer' not in self.user.features:
            raise PermissionDeniedException('User does not have correct feature to access barometer data')

        # build the base query (hard filters)
        response_query = build_response_query(reporting_views, self.global_filters, self.user)
        response_projection = get_response_projection(self.base_response_projection_fields, [self.card_config], round_index_key)

        # get response data
        start_time = time.time()
        trr_response_data = []
        barometer_response_data = []
        if 'trr' in data_required:
            trr_response_data = await fetch_responses(self.db.responses, response_query, response_projection)
        if 'barometer' in data_required:
            barometer_response_data = await fetch_responses(self.db.responsesbarometer, response_query, response_projection)
        elapsed_time = time.time() - start_time
        print(f"EXPORT ({self.dashboard_type}) FETCH DATA: {elapsed_time:.4f} seconds, TRR RECORDS: {len(trr_response_data)}, Barometer RECORDS: {len(barometer_response_data)}")
        if len(trr_response_data) == 0 and len(barometer_response_data) == 0:
            return {'responses': []}

        # generate round lookup
        rounds_lookup_trr = get_round_lookup(trr_response_data, round_index_key)
        rounds_lookup_barometer = get_round_lookup(barometer_response_data, round_index_key)

        # if no round fitler was applied, we can't assume default to 0, so default to latest available round
        if not self.global_filters.get('__round_index'):
            if barometer_dashboard:
                self.global_filters['__round_index'] = min(rounds_lookup_barometer.keys()) if rounds_lookup_barometer else 0
            else:
                self.global_filters['__round_index'] = min(rounds_lookup_trr.keys()) if rounds_lookup_trr else 0

        card_non_response = False
        if self.dashboard_type == 'followup':
            # the followup dashboard is a special case, we need to export non-responders
            card_non_response = True
            self.global_filters['non_responders'] = True

        export_card = ExportResponse(trr_data=trr_response_data,
                                     barometer_data=barometer_response_data,
                                     card_type='ExportResponse',
                                     card_query={},
                                     card_projection=self.card_config['projection'],
                                     card_limit=None,
                                     card_filters=self.card_config['filters'],
                                     card_non_response=card_non_response,
                                     card_previous_rounds=0,
                                     card_data_required=list(data_required),
                                     filtered_round=self.global_filters.get('__round_index'),
                                     global_filters=self.global_filters,
                                     local_filters={},
                                     round_index_key=round_index_key,
                                     trr_norm={},
                                     barometer_norm={})
        export_data = export_card.fetch()

        end_time_all = time.time()
        elapsed_time_all = end_time_all - start_time_all
        print(f"EXPORT LOAD TIME TOTAL: {elapsed_time_all:.4f} seconds for user {self.user.id}")

        return {
            'responses': export_data,
            'dashboard_type': 'Barometer' if barometer_dashboard else 'TRR',
        }
