import datetime
from enum import Enum
from typing import List, Dict, Set, Tuple
from lib.portaldbapi import DocumentDB
from api.clientarea.src.models.responses import PanelResponse
from api.clientarea.src.services.confirmpanel import ConfirmPanelService
from api.clientarea.src.services.markets import MarketsService
from api.clientarea.src.dependencies import User
from api.clientarea.src.models.collections import ConfirmPanel, ConfirmPanelMember
from api.clientarea.src.models.collections import Market
from lib.locale import SF_LANGUAGE_TO_LOCALE_LOOKUP


class ContactSeniority(Enum):
    SENIOR = 'Senior'
    MIDDLE = 'Middle'
    JUNIOR = 'Junior'


class ContactTypes(Enum):
    PRIMARY_CONTACT = 'Primary Contact'
    CRITICAL_CONTACT = 'Critical Contact'
    OTHER = 'Other'


class ContactJobFunction(Enum):
    BUSINESS_DEVELOPMENT = 'Business Development'
    COMMUNICATIONS = 'Communications'
    CLIENT_RELATIONSHIP_MANAGEMENT = 'Client & Relationship Management'
    DATA_ANALYTICS = 'Data & Analytics'
    EXECUTIVE_SENIOR_MANAGEMENT = 'Executive & Senior Management'
    FINANCE = 'Finance'
    HEALTHCARE = 'Healthcare'
    HUMAN_RESOURCES_TALENT_MGMT = 'Human Resources & Talent Mgmt'
    INFORMATION_TECHNOLOGY = 'Information Technology'
    MARKETING = 'Marketing'
    OPERATIONS = 'Operations'
    PRODUCT_DEVELOPMENT = 'Product Development'
    SALES_COMMERCIAL = 'Sales & Commercial'


class ContactJobFunctionMfv(Enum):
    ADMINISTRATION = 'Administration'
    FINANCE = 'Finance'
    MARKETING = 'Marketing'
    SALES_COMMERCIAL = 'Sales & Commercial'
    MANUFACTURING = 'Manufacturing'
    PROCUREMENT_PURCHASING = 'Procurement & Purchasing'
    SUPPLY_CHAIN = 'Supply Chain & Logistics'
    RESEARCH_DEVELOPMENT = 'R&D'
    ENGINEERING = 'Engineering'
    LEGAL = 'Legal, Risk & Compliance'
    EXECTIVE_SENIOR_MANAGEMENT = 'Executive & Senior Management'
    QUALITY_ASSURANCE = 'Quality Assurance'


class GetPanelHandler:
    def __init__(self, survey_id: str, user: User, documentdb: DocumentDB):
        self.survey_id = survey_id
        self.user = user
        self.db = documentdb


    def __has_confirmation_date_passed(self, confirmation_date: datetime.datetime) -> bool:
        confirmation_date:datetime.datetime = confirmation_date.replace(tzinfo=None)
        today:datetime.datetime = datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)
        return confirmation_date < today
    

    def __generate_client_idenfitifers(self, cp_client_id: str, cp_client_name: str, cp_team_id: str, cp_team_name: str, cp_team_market: str, vertical:str) -> Tuple:
        client_key:str = cp_client_id
        client_name:str = cp_client_name
        team_id:str = 'xxx' 
        if cp_team_id:
            team_id:str = cp_team_id
            client_key:str = f'{cp_client_id}-{cp_team_id}'
            if vertical == 'adv':
                client_name:str = f'{client_name} ({cp_team_name} - {cp_team_market})'
        
        return client_key, client_name, team_id


    def __add_confirmation_state_panel_member(self, confirm_panel:ConfirmPanel, panel_member:ConfirmPanelMember, client_name: str, team_id: str, selected: bool) -> Dict:
        job_function:str = panel_member.contact_job_title
        if panel_member.contact_job_function_2:
            job_function:str = panel_member.contact_job_function_2
        
        contact_language:str = panel_member.contact_language
        if not contact_language:
            contact_language:str = confirm_panel.client_market_language

        return dict(
            internal_id=confirm_panel.Id,
            key=f'{confirm_panel.client_id}-{team_id}-{confirm_panel.account_id}-{panel_member.contact_id}',
            contact_id=panel_member.contact_id,
            contact_name=panel_member.contact_name,
            contact_email=panel_member.contact_email,
            contact_type=panel_member.contact_type,
            contact_seniority=panel_member.contact_seniority,
            contact_language=contact_language,
            contact_job_function=panel_member.contact_job_function,
            contact_job_title=job_function,
            contact_division=panel_member.contact_division,
            contact_account_site=panel_member.contact_account_site,
            contact_account_site_id=panel_member.contact_account_site_id,
            contact_location=panel_member.contact_location,
            contact_location_id=panel_member.contact_location_id,
            confirm_panel_id=confirm_panel.Id,
            client_id=confirm_panel.client_id,
            client_name=client_name,
            client_organisation_level_3_name=confirm_panel.client_organisation_level.level_3.name,
            client_organisation_level_4_name=confirm_panel.client_organisation_level.level_4.name,
            client_organisation_level_5_name=confirm_panel.client_organisation_level.level_5.name,
            account_id=confirm_panel.account_id,
            account_name=confirm_panel.account_name,
            team_id=team_id,
            team_name=confirm_panel.team_name,
            team_market=confirm_panel.team_market,
            survey_name=confirm_panel.survey_name,
            serial_non_responder=panel_member.serial_non_responder,
            response_status=panel_member.response_status,
            bounced_status=panel_member.bounced_status,
            in_round=panel_member.in_round,
            selected=selected,
            label=panel_member.contact_email,
            value=panel_member.contact_id,
        )


    def __add_confirmed_state_panel_member(self, confirm_panel:ConfirmPanel, panel_member:ConfirmPanelMember, client_name: str, team_id: str) -> Dict:
        job_function:str = panel_member.contact_job_title
        if panel_member.contact_job_function_2:
            job_function:str = panel_member.contact_job_function_2

        return dict(
            internal_id=confirm_panel.Id,
            key=f'{confirm_panel.client_id}-{team_id}-{confirm_panel.account_id}-{panel_member.contact_id}',
            contact_id=panel_member.contact_id,
            contact_name=panel_member.contact_name,
            contact_email=panel_member.contact_email,
            contact_type=panel_member.contact_type,
            contact_seniority=panel_member.contact_seniority,
            contact_language=panel_member.contact_language,
            contact_job_function=panel_member.contact_job_function,
            contact_job_title=job_function,
            contact_division=panel_member.contact_division,
            contact_account_site=panel_member.contact_account_site,
            contact_account_site_id=panel_member.contact_account_site_id,
            contact_location=panel_member.contact_location,
            contact_location_id=panel_member.contact_location_id,
            confirm_panel_id=confirm_panel.Id,
            client_id=confirm_panel.client_id,
            client_name=client_name,
            client_organisation_level_3_name=confirm_panel.client_organisation_level.level_3.name,
            client_organisation_level_4_name=confirm_panel.client_organisation_level.level_4.name,
            client_organisation_level_5_name=confirm_panel.client_organisation_level.level_5.name,
            account_id=confirm_panel.account_id,
            account_name=confirm_panel.account_name,
            team_id=team_id,
            team_name=confirm_panel.team_name,
            team_market=confirm_panel.team_market,
            survey_name=confirm_panel.survey_name,
            serial_non_responder=panel_member.serial_non_responder,
            response_status=panel_member.response_status,
            bounced_status=panel_member.bounced_status,
        )
    

    def __strip_confirmed_state_duplicate_panel_members(self, panel: list[dict]) -> List[Dict]:
        merged_panel:Dict = {}
        for item in panel:
            key:str = item['key']
            merged_panel[key] = item

        return sorted([value for value in merged_panel.values()], key=lambda x: (x['client_name'], x['contact_email']))


    def __sort_and_dedupe_audit(self, confirmed_audit:List) -> List:
        confirmed_date:str|None = None
        confirmed_user:str|None = None
        confirmed_email:str|None = None
        confirmed_audit_dedupe:List = []
        merged:Dict = {}

        if confirmed_audit:
            confirmed_audit.sort(key=lambda d: d['date'], reverse=True)

            # since users can save multiple customer surveys at once, we want to dedupe the list
            for item in confirmed_audit:
                saved_user:str = item['email']
                saved_date:str = item['date'].strftime('%Y-%m-%d %H:%M:%S')
                key:Tuple = (saved_user, saved_date)

                item:Dict = {**item, 'date': saved_date}
                item_added:List = item.get('added', [])
                item_removed:List = item.get('removed', [])

                if key not in merged:
                    merged[key] = item
                    merged[key]['added'] = item_added.copy()
                    merged[key]['removed'] = item_removed.copy()
                else:
                    existing_added = {
                        (obj['id'], obj['account'], obj['client'])
                        for obj in merged[key]['added']
                    }
                    existing_removed = {
                        (obj['id'], obj['account'], obj['client'])
                        for obj in merged[key]['removed']
                    }

                    for obj in item_added:
                        obj_key = (obj['id'], obj['account'], obj['client'])
                        if obj_key not in existing_added:
                            merged[key]['added'].append(obj)

                    for obj in item_removed:
                        obj_key = (obj['id'], obj['account'], obj['client'])
                        if obj_key not in existing_removed:
                            merged[key]['removed'].append(obj)

            confirmed_audit_dedupe:List = list(merged.values())

            confirmed_date:str = confirmed_audit_dedupe[0].get('date')
            confirmed_user:str = confirmed_audit_dedupe[0].get('user')
            confirmed_email:str = confirmed_audit_dedupe[0].get('email')  

        return confirmed_audit_dedupe, confirmed_date, confirmed_user, confirmed_email  


    def handler(self):
        panel:List = []
        f_clients:List[Dict] = []
        f_accounts:List[Dict] = []
        f_contacts:List[Dict] = []
        f_contact_types:List[Dict] = [{'value': e.value, 'label': e.value} for e in ContactTypes]
        f_contact_seniority:List[Dict] = [{'value': e.value, 'label': e.value} for e in ContactSeniority]
        f_market_country:List[Dict] = []
        f_market_city:List[Dict] = []
        f_contact_job_function:List[Dict] = []
        f_languages:List[Dict] = []
        confirmed_audit:List[Dict] = []
        confirmed_status:bool = False
        complete_by_date:datetime.datetime|None = None
        complete_by_days:int = 0
        account_vertical:str = self.user.account_vertical
        customer_survey_round_name:str|None = None
        am_view_only:bool = False
        confirm_panels_pm:List = []
        confirm_panels_pm_active:List = []
        confirm_panels_pm_closed:List = []
        confirm_panels_am:List = []
        confirm_panels_am_active:List = []
        confirm_panels_am_closed:List = []
        
        # grab the confirm panels from the db
        confirm_panels:Dict = ConfirmPanelService(self.db).get_by_user_involvement(self.survey_id, self.user.id)
        confirm_panels_pm:List[ConfirmPanel] = confirm_panels.get('panel_manager', [])
        confirm_panels_am:List[ConfirmPanel] = confirm_panels.get('account_manager', [])

        # split the confirm panels into active and closed for both role types
        for confirm_panel in confirm_panels_pm:
            if self.__has_confirmation_date_passed(confirm_panel.panel_confirmed_by_date):
                confirm_panels_pm_closed.append(confirm_panel)
            else:
                confirm_panels_pm_active.append(confirm_panel)

        for confirm_panel in confirm_panels_am:
            if self.__has_confirmation_date_passed(confirm_panel.panel_confirmed_by_date):
                confirm_panels_am_closed.append(confirm_panel)
            else:
                confirm_panels_am_active.append(confirm_panel)

        # get the customer survey round name
        if confirm_panels_pm:
            customer_survey_round_name:str = confirm_panels_pm[0].customer_survey_round_name
        elif confirm_panels_am:
            customer_survey_round_name:str = confirm_panels_am[0].customer_survey_round_name

        # If confirm_panels_pm_active, use it
        if confirm_panels_pm_active:
            survey_name_lookup:Dict = {}

            # get earliest date for panels closing
            complete_by_date:datetime.datetime = min([d.panel_confirmed_by_date for d in confirm_panels_pm_active], default=None)

            for confirm_panel in confirm_panels_pm_active:
                # generate dynamic identifiers around the client
                client_key, client_name, team_id = self.__generate_client_idenfitifers(confirm_panel.client_id, 
                                                                                       confirm_panel.client_name, 
                                                                                       confirm_panel.team_id, 
                                                                                       confirm_panel.team_name, 
                                                                                       confirm_panel.team_market, 
                                                                                       account_vertical)

                # if there has been a confirmation, use that data to pre-populate the panel
                if len(confirm_panel.confirmed_panel) > 0:
                    # get list of all edits
                    for confirm in confirm_panel.confirmed_panel:
                        audit:Dict = {
                            'user': confirm.confirm_user_name, 
                            'email': confirm.confirm_user_email, 
                            'date': confirm.confirm_date.replace(microsecond=0)
                        }
                        # FIXME: once the data has been migrated, we can remove this and add it above
                        if confirm.stats:
                            audit['added'] = confirm.stats['added']
                            audit['removed'] = confirm.stats['removed']
                        confirmed_audit.append(audit)

                    # NOTE: Mongo scaling fix - if required (no rollout now to reduce risk)
                    # the first in the list will be the latest
                    #latest_confirmed_panel = confirm_panel.confirmed_panel_last_save_all_panel
                    #if latest_confirmed_panel is None:
                    #    latest_confirmed_panel = confirm_panel.confirmed_panel[0].all_panel
                    latest_confirmed_panel:List[Dict] = confirm_panel.confirmed_panel[0].all_panel

                    # get a list of all the selected confirmed panel members
                    selected_panel:List[str] = [panel_member.contact_id for panel_member in confirm_panel.confirmed_panel[0].panel]
                    
                    for panel_member in latest_confirmed_panel:
                        is_selected = bool(panel_member.contact_id in selected_panel)
                        panel.append(self.__add_confirmation_state_panel_member(confirm_panel, panel_member, client_name, team_id, is_selected))
                # if no confirmations, just use the default panels
                else:
                    for panel_member in confirm_panel.panel:
                        survey_name_lookup[f'{confirm_panel.client_id}-{confirm_panel.account_id}'] = panel_member.survey_name

                        res:Dict = self.__add_confirmation_state_panel_member(confirm_panel, panel_member, client_name, team_id, True)

                        # if the panel member is "in_round" (meaning they were surveyed in the prior round) then we want to add them to the "panel" list for pre-population
                        if panel_member.in_round:
                            panel.append(res)  
                        else:
                            f_contacts.append(res)

                # add in any panel members that have been added in SF
                for panel_member in confirm_panel.panel:
                    panel_member_ids:List[str] = [panel_member['contact_id'] for panel_member in panel]
                    filter_contact_ids:List[str] = [panel_member['contact_id'] for panel_member in f_contacts]
                    if panel_member.contact_id not in panel_member_ids and panel_member.contact_id not in filter_contact_ids:
                        res:Dict = self.__add_confirmation_state_panel_member(confirm_panel, panel_member, client_name, team_id, True)
                        if panel_member.in_round:   
                            panel.append(res)
                        else:
                            f_contacts.append(res)
            
                f_clients.append(dict(
                    key=client_key,
                    id=confirm_panel.client_id, 
                    name=client_name
                ))

                f_accounts.append(dict(
                    key=f'{confirm_panel.client_id}-{team_id}-{confirm_panel.account_id}',
                    id=confirm_panel.account_id, 
                    name=confirm_panel.account_name,
                    client_id=confirm_panel.client_id,
                    client_name=client_name,
                    client_organisation_level_3_name=confirm_panel.client_organisation_level.level_3.name,
                    client_organisation_level_4_name=confirm_panel.client_organisation_level.level_4.name,
                    client_organisation_level_5_name=confirm_panel.client_organisation_level.level_5.name,
                    market=confirm_panel.client_market,
                    language=confirm_panel.client_market_language,
                    survey_name=confirm_panel.survey_name,
                    confirm_panel_id=confirm_panel.Id,
                    value=f'{confirm_panel.client_id}-{team_id}-{confirm_panel.account_id}',
                    label=f'{client_name} - {confirm_panel.account_name}'
                ))

            # get locales for language selection
            f_languages:List[Dict] = [{'value': key, 'label': key} for key, value in SF_LANGUAGE_TO_LOCALE_LOOKUP.items()]

            # get markets
            markets:List[Market] = MarketsService(self.db).get_markets()
            f_market_country:List[Dict] = [{'value': market.Id, 'label': market.name} for market in MarketsService(self.db).filter_markets_by_type(markets, 'Country')]
            f_market_city:List[Dict] = [{'value': market.Id, 'label': market.name} for market in MarketsService(self.db).filter_markets_by_type(markets, 'City')]

            # get contact job functions based on vertical
            if account_vertical == 'mfv':
                f_contact_job_function:List = [{'value': e.value, 'label': e.value} for e in ContactJobFunctionMfv]
            else:
                f_contact_job_function:List = [{'value': e.value, 'label': e.value} for e in ContactJobFunction]

            # strip client duplicates
            f_clients:List = [dict(t) for t in {tuple(d.items()) for d in f_clients}]

            # sort filters
            f_clients:List = sorted(f_clients, key=lambda x: x['name'])
            f_accounts:List = sorted(f_accounts, key=lambda x: x['name'])
            f_contacts:List = sorted(f_contacts, key=lambda x: x['contact_name'])
            f_contact_seniority:List = sorted(f_contact_seniority, key=lambda x: x['label'])
            f_contact_job_function:List = sorted(f_contact_job_function, key=lambda x: x['label'])
            f_contact_types:List = sorted(f_contact_types, key=lambda x: x['label'])
            f_market_country:List = sorted(f_market_country, key=lambda x: x['label'])
            f_market_city:List = sorted(f_market_city, key=lambda x: x['label'])
            f_languages:List = sorted(f_languages, key=lambda x: x['label'])
        # If confirm_panels_am_active, use it -> READ-ONLY STATE
        elif confirm_panels_am_active:
            am_view_only:bool = True
            confirmed_status:bool = True
            panel:List = []
            confirmed_audit:List = []

            # get earliest date for panels closing
            complete_by_date:datetime.datetime = min([d.panel_confirmed_by_date for d in confirm_panels_am_active], default=None)

            for confirm_panel in confirm_panels_am_active:
                # generate dynamic identifiers around the client
                client_key, client_name, team_id = self.__generate_client_idenfitifers(confirm_panel.client_id, 
                                                                                       confirm_panel.client_name, 
                                                                                       confirm_panel.team_id, 
                                                                                       confirm_panel.team_name, 
                                                                                       confirm_panel.team_market, 
                                                                                       account_vertical)
                
                if len(confirm_panel.confirmed_panel) > 0:
                    for confirm in confirm_panel.confirmed_panel:
                        audit:Dict = {
                            'user': confirm.confirm_user_name, 
                            'email': confirm.confirm_user_email, 
                            'date': confirm.confirm_date.replace(microsecond=0)
                        }
                        # FIXME: once the data has been migrated, we can remove this and add it above
                        if confirm.stats:
                            audit['added'] = confirm.stats['added']
                            audit['removed'] = confirm.stats['removed']
                        confirmed_audit.append(audit)

                    latest_confirmed_panel:List = confirm_panel.confirmed_panel[0].panel

                    for panel_member in latest_confirmed_panel:
                        panel.append(self.__add_confirmed_state_panel_member(confirm_panel, panel_member, client_name, team_id))
        # If confirm_panels_pm_closed, use it
        elif confirm_panels_pm_closed or confirm_panels_am_closed:
            confirmed_status:bool = True
            panel:List = []
            confirmed_audit:List = []

            # get earliest date for panels closing
            if confirm_panels_pm_closed:
                complete_by_date:datetime.datetime = min([d.panel_confirmed_by_date for d in confirm_panels_pm_closed], default=None)
            elif confirm_panels_am_closed:
                complete_by_date:datetime.datetime = min([d.panel_confirmed_by_date for d in confirm_panels_am_closed], default=None)

            for confirm_panel in confirm_panels_pm_closed:
                 # generate dynamic identifiers around the client
                client_key, client_name, team_id = self.__generate_client_idenfitifers(confirm_panel.client_id, 
                                                                                       confirm_panel.client_name, 
                                                                                       confirm_panel.team_id, 
                                                                                       confirm_panel.team_name, 
                                                                                       confirm_panel.team_market, 
                                                                                       account_vertical)

                if len(confirm_panel.confirmed_panel) > 0:
                    for confirm in confirm_panel.confirmed_panel:
                        audit:Dict = {
                            'user': confirm.confirm_user_name, 
                            'email': confirm.confirm_user_email, 
                            'date': confirm.confirm_date.replace(microsecond=0)
                        }
                        # FIXME: once the data has been migrated, we can remove this and add it above
                        if confirm.stats:
                            audit['added'] = confirm.stats['added']
                            audit['removed'] = confirm.stats['removed']
                        confirmed_audit.append(audit)
                
                for panel_member in confirm_panel.final_confirmed_panel:
                    panel.append(self.__add_confirmed_state_panel_member(confirm_panel, panel_member, client_name, team_id))
            
            for confirm_panel in confirm_panels_am_closed:
                 # generate dynamic identifiers around the client
                client_key, client_name, team_id = self.__generate_client_idenfitifers(confirm_panel.client_id, 
                                                                                       confirm_panel.client_name, 
                                                                                       confirm_panel.team_id, 
                                                                                       confirm_panel.team_name, 
                                                                                       confirm_panel.team_market, 
                                                                                       account_vertical)

                if len(confirm_panel.confirmed_panel) > 0:
                    for confirm in confirm_panel.confirmed_panel:
                        audit:Dict = {
                            'user': confirm.confirm_user_name, 
                            'email': confirm.confirm_user_email, 
                            'date': confirm.confirm_date.replace(microsecond=0)
                        }
                        # FIXME: once the data has been migrated, we can remove this and add it above
                        if confirm.stats:
                            audit['added'] = confirm.stats['added']
                            audit['removed'] = confirm.stats['removed']
                        confirmed_audit.append(audit)
                
                for panel_member in confirm_panel.final_confirmed_panel:
                    panel.append(self.__add_confirmed_state_panel_member(confirm_panel, panel_member, client_name, team_id))

        # check for duplicates in the panel via the "key" field and merge into one
        panel:List = self.__strip_confirmed_state_duplicate_panel_members(panel)

        # sort audit and get latest save
        confirmed_audit_dedupe, confirmed_date, confirmed_user, confirmed_email = self.__sort_and_dedupe_audit(confirmed_audit)

        # calculate days remaining until panels close
        if complete_by_date:
            complete_by_date:datetime.datetime = complete_by_date.replace(tzinfo=None)
            complete_by_days:int = (complete_by_date - datetime.datetime.now().replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=None)).days + 1

        return PanelResponse(
            customer_survey_round_name=customer_survey_round_name,
            user_account_name=self.user.account_name,
            action_required=bool(len(confirm_panels_pm) > 0 or len(confirm_panels_am) > 0),
            complete_by_date=complete_by_date,
            complete_by_days=complete_by_days,
            panel=panel,
            confirmed_audit=confirmed_audit_dedupe,
            confirmed_user=confirmed_user,
            confirmed_email=confirmed_email,
            confirmed_date=confirmed_date,
            confirmed=confirmed_status,
            account_vertical=account_vertical,
            am_view_only=am_view_only,
            f_clients=f_clients,
            f_accounts=f_accounts,
            f_contacts=f_contacts,
            f_contact_types=f_contact_types,
            f_contact_seniority=f_contact_seniority,
            f_contact_job_function=f_contact_job_function,
            f_market_country=f_market_country,
            f_market_city=f_market_city,
            f_languages=f_languages,
        )
