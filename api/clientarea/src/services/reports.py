import boto3
from urllib.parse import quote
from litestar.exceptions import HTTPException
from lib.settings import settings
from lib.portaldbapi import DocumentDB, SrpReport
from api.clientarea.src.dependencies import User


class SrpReportsService:
    def __init__(self, user: User, db: DocumentDB):
        self.db = db
        self.user = user
        self.collection = db.srpreports


    def get_reports(self) -> list:
        reports = []

        # if user has no report views, bail
        if not self.user.reporting_views:
            return reports
        
        query = {
            'contact_id': self.user.id,
            'published': True,
            'deleted': {'$ne': True}
        }
        projection = {
            '_id': 0
        }

        reports_raw = [
            SrpReport(**report)
            for report in self.collection.find(query, projection)
        ]

        # just need id, title, csr, published_date
        for report in reports_raw:
            title = report.title

            if title.endswith(".pdf"):
                title = title[:-4]

            reports.append(
            {
                'id': report.Id,
                'title': title,
                'customer_survey_round': report.customer_survey_round_title,
                'published_date': report.published_date.strftime('%Y-%m-%d')
            })

        # order reports by published date
        reports = sorted(reports, key=lambda x: x['published_date'], reverse=True)

        return reports
    
    
    def get_report(self, report_id: str) -> dict:
        s3_session = boto3.Session()
        s3 = s3_session.client('s3')

        query = {
            'Id': report_id,
            'contact_id': self.user.id,
            'published': True,
            'deleted': {'$ne': True}
        }

        report = self.collection.find_one(query, {'_id': 0})

        if not report:
            raise HTTPException(status_code=404, detail='Report not found')

        try:
            title = report.get("title", None)
            if not title:
                title = report["s3_filename"]
            title = title.strip()
            title = title.replace(" ", "_")

            # some reports have a .pdf extension, some do not
            # TODO: move this to srp sync agent
            if not title.lower().endswith(".pdf"):
                title += ".pdf"

            quoted_filename = quote(title)

            presigned_url = s3.generate_presigned_url(
                "get_object",
                Params={
                    "Bucket": settings.SF_SRP_REPORTS_BUCKET, 
                    "Key": report["s3_filename"],
                    "ResponseContentDisposition": f'attachment; filename="{quoted_filename}"'
                },
                ExpiresIn=300,
            )

            return {'url': presigned_url}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))