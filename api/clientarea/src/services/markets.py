from typing import Dict
from api.clientarea.src.models.collections import Market


class MarketsService():
    def __init__(self, db):
        self.db = db

    def get_markets(self, projection:Dict[str,int] = {'_id': 0, 'children': 0}) -> list[Market]:
        return [
            Market(**market)
            for market in self.db.markets.find({}, projection)
        ]
    
    def get_key_region_for_market(self, markets: list[Market], market_id: str) -> Dict:
        market_id_map = {market.Id: market for market in markets}
        current_market = market_id_map.get(market_id)

        if not current_market:
            raise ValueError(f"Market with id {market_id} not found.")

        if current_market.type not in ['City', 'Country', 'Region', 'Business Region', 'Key Region']:
            raise ValueError(f"Market with id {market_id} is not a City, Country, Region, Business Region or Key Region")

        while current_market and current_market.parent_market_id:
            parent_id = current_market.parent_market_id
            current_market = market_id_map.get(parent_id)

            if not current_market:
                raise ValueError(f"Parent market with id {parent_id} not found.")

            if current_market.type == 'Key Region':
                return current_market

        raise ValueError("Key Region not found in hierarchy.")
    
    def filter_markets_by_type(self, markets: list[Market], market_type: str) -> list[Market]:
        return [market for market in markets if market.type == market_type]
