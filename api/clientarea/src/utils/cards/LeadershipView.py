from api.clientarea.src.utils.cards.Card import Card
from api.clientarea.src.utils.locale import get_locale


class LeadershipView(Card):

    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    
    
    def _response_rate(self, responses, total):
        if total == 0:
            return 0
        response_rate = round((responses / total) * 100, 2)
        response_rate = int(response_rate) if response_rate.is_integer() else response_rate
        return response_rate
    

    def __calculateHcKey(self, country):
        return get_locale(country)

    def calc_aggregate_metrics(self, responses: list, dimension_name: str, round_index: str, rounds: dict = {}) -> list:
        grouped_data = {}
        id_property = f'{dimension_name}_id'
        name_property = f'{dimension_name}_name'

        if dimension_name == 'market_country':
            # country name is stored as market_country, not market_country_name
            # TODO should probably fix this in the sync response agent, but will mean updating dashboard config in db to match
            name_property = 'market_country'
        elif dimension_name == 'contact_division':
            # we don't have name or id fields for contact_division, so we'll use the dimension name
            id_property = name_property = dimension_name

        for record in responses:
            dimension_id = record.get(id_property)
            name = record.get(name_property)

            if dimension_name == 'market_and_team':
                country = record.get('market_country', '')
                team_name = record.get('team_name', '')

                if country and team_name:
                    dimension_id = team_name
                    name = f"{country} - {team_name}"
                else:
                    dimension_id = country
                    name = country

            # if dimension_name == 'market':
            #    if record.get('team_market_name', None):
            #        dimension_id = record['team_market_name']
            #        name = record['team_market_name']
            #    else:
            #        dimension_id = record['market_name']
            #        name = record['market_name']

            # handle nulls, group under "Unknown"
            if not dimension_id:
                dimension_id = 'unknown'
                name = 'Unknown'

            rating = 0 if not record.get('rating', None) else record.get('rating', 0)

            if name not in grouped_data:
                grouped_data[name] = {
                    'name': name,
                    'id': dimension_id,
                    'rating': 0,
                    'rating_avg': 0,
                    'responses': 0,
                    'outreach': 0,
                    'lr_rating': 0,
                    'lr_rating_avg': 0,
                    'lr_responses': 0,
                    'in_latest_round': False,
                }

            if rounds[record[round_index]] == 'current':
                grouped_data[name]['outreach'] += 1

            if record['responded']:
                if rounds[record[round_index]] == 'current':
                    grouped_data[name]['responses'] += 1
                    grouped_data[name]['rating'] += rating
                    grouped_data[name]['rating_avg'] = grouped_data[name]['rating'] / grouped_data[name]['responses']
                    grouped_data[name]['in_latest_round'] = True
                elif rounds[record[round_index]] == 'previous':
                    grouped_data[name]['lr_responses'] += 1
                    grouped_data[name]['lr_rating'] += rating
                    grouped_data[name]['lr_rating_avg'] = grouped_data[name]['lr_rating'] / grouped_data[name]['lr_responses']
            else:
                if rounds[record[round_index]] == 'current':
                    grouped_data[name]['in_latest_round'] = True

        # remove data with not in the latest round
        filtered_data = {k: v for k, v in grouped_data.items() if v['in_latest_round']}

        grouped_response = [
            {
                'rating': data['rating_avg'],
                'name': name,
                'id': data['id'],
                'response_rate': f'{self._response_rate(data['responses'], data['outreach'])}%',
                'movement': self.__rating_change(data['rating_avg'], data['lr_rating_avg'])
            } for name, data in filtered_data.items()
        ]
        grouped_response = sorted(grouped_response, key=lambda x: x['rating'], reverse=True)
        return grouped_response


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        groupings = {
            'account': [], 
            'agency_brand':[], 
            'client': [], 
            'contact_division': [], 
            'holding_group': [], 
            'market_country': [],
            'market_and_team': [],
            'network': [],
            # 'office': [],
            'sub_network': [], 
            'team': [],
        }
        if not data:
            return groupings

        for key in groupings.keys():
            groupings[key] = self.calc_aggregate_metrics(data, key, round_index, rounds)

        return groupings


    def format(self, data: dict):
        result = {}
        for dimension, dimension_data in data.items():
            chart_data = []
            for record in dimension_data:
                chart_data.append({
                    'name': record['name'],
                    'id': record['id'],
                    'rating_avg': round(record['rating'], 2),
                    'response_rate': record['response_rate'],
                    'movement': record['movement']
                })
            # ignore any groupings that only have an entry for 'unknown'
            if len(chart_data) == 1 and chart_data[0]['id'] == 'unknown':
                continue
            result.setdefault(dimension, {})['chart'] = chart_data

            if dimension == 'market_country':
                # country level data is also shown on a map
                map_data = []
                for record in dimension_data:
                    map_data.append({
                        'hc-key': self.__calculateHcKey(record['name']),
                        'value': round(record['rating'], 2)
                    })
                # save the country map data in the market key
                result.setdefault(dimension, {})['map'] =  map_data
        return result
