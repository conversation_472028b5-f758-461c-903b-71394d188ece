import copy
from abc import ABC, abstractmethod
from functools import partial
from typing import Optional


class Card(ABC):
    def __new__(cls, *args, **kwargs):
        instance = super(Card, cls).__new__(cls)
        Card.__init__(instance, *args, **kwargs)
        return instance


    def __init__(self,
                 trr_data,
                 barometer_data,
                 card_type,
                 card_query,
                 card_projection,
                 card_limit,
                 card_filters,
                 card_non_response,
                 card_previous_rounds,
                 card_data_required,
                 filtered_round,
                 global_filters, 
                 local_filters,
                 round_index_key,
                 trr_norm,
                 barometer_norm,
                 **kwargs):
        self.trr_data = trr_data
        self.barometer_data = barometer_data
        self.card_type = card_type
        self.card_query = card_query
        self.card_projection = card_projection
        self.card_limit = card_limit
        self.card_previous_rounds = card_previous_rounds
        self.card_data_required = card_data_required
        self.filtered_round = filtered_round
        self.affected_filters = card_filters
        self.filters = {**global_filters, **local_filters}
        self.round_index_key = round_index_key
        self.trr_norm = trr_norm
        self.barometer_norm = barometer_norm
        self.include_non_response = card_non_response
        
        self.rounds = {}
        self.rounds_barometer = {}
        # for linking CSRs in the TRR and Barometer data for 360 cards
        self.trr_csr_ids = None
    

    def __filtered_data(self, responses: list, filtered_round: int, rounds: dict, csr_ids: Optional[set] = None):
        filtered_data = []
        # we need the round index key at a minimum for filterting
        projection = {self.round_index_key: 1}
        # this method may be called twice, so avoid side-effects
        card_query = copy.deepcopy(self.card_query)

        if self.card_projection:
            projection = projection | self.card_projection

        for filter in self.filters:
            if filter in self.affected_filters and self.filters[filter] is not None:
                card_query[filter] = self.filters[filter]

        if self.include_non_response == False:
            card_query['responded'] = True

        if csr_ids is not None:
            card_query['customer_survey_round_id'] = list(csr_ids)
        elif self.card_previous_rounds:
            round_indexes = list(range(filtered_round, (filtered_round + self.card_previous_rounds)+1))
            for round in round_indexes:
                rounds[round] = None
            rounds[filtered_round] = 'current'
            rounds[filtered_round+1] = 'previous'
            card_query[self.round_index_key] = round_indexes
        else:
            card_query[self.round_index_key] = filtered_round

        for response in responses:
            if (
                response.get("is_extra_question", False) and
                projection.get("is_extra_question", 0) != 1
            ):
                continue
            matches = 0
            total_queries = len(card_query)
            for param in card_query:
                if isinstance(card_query[param], list) and isinstance(response[param], list):
                    if any(item in response[param] for item in card_query[param]):
                        matches += 1
                elif isinstance(card_query[param], list):
                    if response[param] in card_query[param]:
                        matches += 1
                else:
                    if response[param] == card_query[param]:
                        matches += 1
            if matches == total_queries:
                projected_response = {key: response[key] for key in projection if key in response}
                filtered_data.append(projected_response)

        print(f'>>> DASHBOARD CARD DATA FETCHED: {self.card_type} - {len(filtered_data)} records')

        return filtered_data


    @abstractmethod
    def get_data(self, data: list, round_index: str, rounds: dict = {}, filters: dict = {}, norm: dict = {}):
        pass


    def format(self, data: dict):
        return data


    def fetch(self):
        filtered_data_trr = []
        filtered_data_barometer = []
        if 'barometer' in self.card_data_required and 'trr' in self.card_data_required:
            # card is showing both barometer and trr data, we need to filter the trr data based on
            # the CSR id(s) the barometer data links to rather than the round(s)
            self.trr_csr_ids = set()

        if 'barometer' in self.card_data_required:
            filtered_data_barometer = self.__filtered_data(self.barometer_data, self.filtered_round, self.rounds_barometer, None)
            for x in filtered_data_barometer:
                # add a property to allow cards to separate trr and baro response
                x['is_barometer'] = True
                if x.get('linked_trr_survey_id') and self.trr_csr_ids is not None:
                    # if the barometer response has a linked trr survey id, add it to the set
                    self.trr_csr_ids.add(x['linked_trr_survey_id'])

        if 'trr' in self.card_data_required:
            filtered_data_trr = self.__filtered_data(self.trr_data, self.filtered_round, self.rounds, self.trr_csr_ids)

        # if we are showing both barometer and trr data, we need to merge the two datasets, with the barometer data first
        # as some cards rely on this order
        filtered_data = filtered_data_barometer + filtered_data_trr
        # TODO signature of the get_data method should really change, but don't want to have to update every widget at the moment
        data = self.get_data(filtered_data, self.round_index_key, rounds=self.rounds, filters=self.filters, norm=self.trr_norm)
        format = self.format(data)
        return format
