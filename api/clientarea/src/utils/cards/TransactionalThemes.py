from api.clientarea.src.utils.cards.Card import Card


class TransactionalThemes(Card):
    
    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        count = 0
        categories = {}
        
        for record in data:
            index = record[round_index]
            rating = 0 if not record.get('rating', None) else record.get('rating', 0)
            if rounds[index] == 'current':
                count += 1

            for theme in record.get('themes', []):
                category_l1 = theme['l1']
                category_l2 = theme['l2']

                if not category_l1 or not category_l2:
                    continue

                if category_l1 != 'Transactional':
                    continue

                if category_l2 not in categories:
                    categories[category_l2] = {
                        'id': f'{record["Id"]}-{category_l2}-{theme['id']}',
                        'responses': 0,
                        'rating': 0,
                        'rating_avg': 0,
                        'mentions': 0,
                        'lr_responses': 0,
                        'lr_rating': 0,
                        'lr_rating_avg': 0,
                        'in_latest_round': False
                    }

                if rounds[index] == 'current':
                    categories[category_l2]['in_latest_round'] = True
                    categories[category_l2]['mentions'] += 1

                    categories[category_l2]['responses'] += 1
                    categories[category_l2]['rating'] += rating
                    categories[category_l2]['rating_avg'] = categories[category_l2]['rating'] / categories[category_l2]['responses']
                elif rounds[index] == 'previous':
                    categories[category_l2]['lr_responses'] += 1
                    categories[category_l2]['lr_rating'] += rating
                    categories[category_l2]['lr_rating_avg'] = categories[category_l2]['lr_rating'] / categories[category_l2]['lr_responses']
        
        if not count:
            return {}
        
        # strip out categories not in latest round
        categories = {k: v for k, v in categories.items() if v['in_latest_round']}

        categories = sorted(categories.items(), key=lambda x: x[1]['rating_avg'], reverse=True)

        return categories
    
    def format(self, data: dict):
        if not data:
            return []
        
        return [
            {
                'theme': key,
                'mentions': category['mentions'],
                'rating_avg': round(category['rating_avg'], 2),
                'movement': self.__rating_change(category['rating_avg'], category['lr_rating_avg'])
            }
            for key, category in data
        ]