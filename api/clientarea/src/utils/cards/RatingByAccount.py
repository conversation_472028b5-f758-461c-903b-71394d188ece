from api.clientarea.src.utils.cards.Card import Card


class RatingByAccount(Card):

    def __round_to_100(self, values):
        rounded_values = [round(value, 2) for value in values]
        total = sum(rounded_values)
    
        difference = round(100 - total, 2)
    
        if difference != 0:
            max_index = max(range(len(rounded_values)), key=lambda i: rounded_values[i])
            rounded_values[max_index] += difference
            rounded_values[max_index] = round(rounded_values[max_index], 2)
        
        return rounded_values
    

    def __rating_change(self, this_round, last_round):
        if this_round == 0 or last_round == 0:
            return 0
        increase = round(this_round, 2) - round(last_round, 2)
        percentage_increase = (increase / last_round) * 100
        return round(percentage_increase, 2)
    
    
    def __calculate_response_rate(self, responses, total):
        if total == 0:
            return 0
        response_rate = round((responses / total) * 100, 2)
        response_rate = int(response_rate) if response_rate.is_integer() else response_rate
        return response_rate


    def __calculate_point_difference(self, this_round, last_round):
        if this_round == 0:
            return 0
        return round(this_round - last_round, 2)
    

    def calc_aggregate_metrics(self, responses: list, dimension_name: str, round_index: str, rounds: dict = {}) -> list:
        grouped_data = {}
        id_property = f'{dimension_name}_id'
        name_property = f'{dimension_name}_name'

        if dimension_name == 'market_country':
            # country name is stored as market_country, not market_country_name
            # TODO should probably fix this in the sync response agent, but will mean updating dashboard config in db to match
            name_property = 'market_country'
        elif dimension_name == 'contact_division':
            # we don't have name or id fields for contact_division, so we'll use the dimension name
            id_property = name_property = dimension_name

        for record in responses:      
            index = record[round_index]      
            dimension_id = record.get(id_property)
            name = record.get(name_property)

            if dimension_name == 'market_and_team':
                country = record.get('market_country', '')
                team_name = record.get('team_name', '')

                if country and team_name:
                    dimension_id = team_name
                    name = f"{country} - {team_name}"
                else:
                    dimension_id = country
                    name = country

            if not dimension_id:
                dimension_id = 'unknown'
                name = 'Unknown'

            if name not in grouped_data:
                grouped_data[name] = {
                    'name': name,
                    'id': dimension_id,
                    'responses': 0,
                    'rating': 0,
                    'rating_avg': 0,
                    'response_rate': 0,
                    'responses_with_themes': 0,
                    'lr_responses': 0,
                    'lr_rating': 0,
                    'lr_rating_avg': 0,
                    'lr_responses_with_themes': 0,
                    'responses_by_primary': 0,
                    'rating_by_primary': 0,
                    'rating_avg_by_primary': 0,
                    'response_rate_by_primary': 0,
                    'responses_by_critical': 0,
                    'rating_by_critical': 0,
                    'rating_avg_by_critical': 0,
                    'response_rate_by_critical': 0,
                    'sentiment_p': 0,
                    'sentiment_n': 0,
                    'sentiment_m': 0, 
                    'lr_sentiment_p': 0,
                    'lr_sentiment_n': 0,
                    'lr_sentiment_m': 0,
                    'total_feedback': 0,
                    'feedback_relational': 0,
                    'feedback_transactional': 0,
                    'feedback_general': 0,
                    'lr_total_feedback': 0,
                    'lr_feedback_relational': 0,
                    'lr_feedback_transactional': 0,
                    'lr_feedback_general': 0,
                    'total_surveyed': 0,
                    'total_surveyed_by_primary': 0,
                    'total_surveyed_by_critical': 0,
                    'in_latest_round': False,
                }

            aggregate_data = grouped_data[name]

            # if it's the latest round, count-up the total surveyed
            if rounds[index] == 'current':
                aggregate_data['total_surveyed'] += 1
                if record['contact_type'] == 'Primary Contact':
                    aggregate_data['total_surveyed_by_primary'] += 1
                if record['contact_type'] == 'Critical Contact':
                    aggregate_data['total_surveyed_by_critical'] += 1

            if record['responded']:
                if rounds[index] == 'current':
                    aggregate_data['responses'] += 1
                    aggregate_data['rating'] += 0 if not record.get('rating', None) else record.get('rating', 0)
                    aggregate_data['rating_avg'] = aggregate_data['rating'] / aggregate_data['responses']
                    aggregate_data['in_latest_round'] = True

                    if record['contact_type'] == 'Primary Contact':
                        aggregate_data['responses_by_primary'] += 1
                        aggregate_data['rating_by_primary'] += 0 if not record.get('rating', None) else record.get('rating', 0)
                        aggregate_data['rating_avg_by_primary'] = aggregate_data['rating_by_primary'] / aggregate_data['responses_by_primary']
                    if record['contact_type'] == 'Critical Contact':
                        aggregate_data['responses_by_critical'] += 1
                        aggregate_data['rating_by_critical'] += 0 if not record.get('rating', None) else record.get('rating', 0)
                        aggregate_data['rating_avg_by_critical'] = aggregate_data['rating_by_critical'] / aggregate_data['responses_by_critical']
                    
                    if record.get('sentiment'):
                        aggregate_data['responses_with_themes'] += 1

                        if record['sentiment'] == 'p':
                            aggregate_data['sentiment_p'] += 1
                        elif record['sentiment'] == 'n':
                            aggregate_data['sentiment_n'] += 1
                        elif record['sentiment'] == 'm':
                            aggregate_data['sentiment_m'] += 1
                elif rounds[index] == 'previous':
                    aggregate_data['lr_responses'] += 1
                    aggregate_data['lr_rating'] += 0 if not record.get('rating', None) else record.get('rating', 0)
                    aggregate_data['lr_rating_avg'] = aggregate_data['lr_rating'] / aggregate_data['lr_responses']

                    if record.get('sentiment'):
                        aggregate_data['lr_responses_with_themes'] += 1

                        if record['sentiment'] == 'p':
                            aggregate_data['lr_sentiment_p'] += 1
                        elif record['sentiment'] == 'n':
                            aggregate_data['lr_sentiment_n'] += 1
                        elif record['sentiment'] == 'm':
                            aggregate_data['lr_sentiment_m'] += 1
                
                for theme in record.get('themes', []):
                    category = theme['l1']

                    if not category:
                        continue

                    if rounds[index] == 'current':
                        aggregate_data['total_feedback'] += 1
                    elif rounds[index] == 'previous':
                        aggregate_data['lr_total_feedback'] += 1

                    if category == 'Relational':
                        if rounds[index] == 'current':
                            aggregate_data['feedback_relational'] += 1
                        elif rounds[index] == 'previous':
                            aggregate_data['lr_feedback_relational'] += 1
                    elif category == 'Transactional':
                        if rounds[index] == 'current':
                            aggregate_data['feedback_transactional'] += 1
                        elif rounds[index] == 'previous':
                            aggregate_data['lr_feedback_transactional'] += 1
                    else:
                        if rounds[index] == 'current':
                            aggregate_data['feedback_general'] += 1
                        elif rounds[index] == 'previous':
                            aggregate_data['lr_feedback_general'] += 1

        # strip out any grouping not in the latest round
        grouped_data = {k: v for k, v in grouped_data.items() if v['in_latest_round']}
        
        for _, aggregate_data in grouped_data.items():
            if aggregate_data['responses_with_themes'] > 0:
                sentiment_rounded_values = self.__round_to_100([
                    (aggregate_data['sentiment_p'] / aggregate_data['responses_with_themes'] * 100),
                    (aggregate_data['sentiment_n'] / aggregate_data['responses_with_themes'] * 100),
                    (aggregate_data['sentiment_m'] / aggregate_data['responses_with_themes'] * 100),
                ])
                aggregate_data['sentiment_p'] = sentiment_rounded_values[0]
                aggregate_data['sentiment_n'] = sentiment_rounded_values[1]
                aggregate_data['sentiment_m'] = sentiment_rounded_values[2]

                feedback_rounded_values = self.__round_to_100([
                    (aggregate_data['feedback_relational'] / aggregate_data['total_feedback'] * 100),
                    (aggregate_data['feedback_transactional'] / aggregate_data['total_feedback'] * 100),
                    (aggregate_data['feedback_general'] / aggregate_data['total_feedback'] * 100),
                ])
                aggregate_data['feedback_relational'] = feedback_rounded_values[0]
                aggregate_data['feedback_transactional'] = feedback_rounded_values[1]
                aggregate_data['feedback_general'] = feedback_rounded_values[2]

            if aggregate_data['lr_sentiment_p'] > 0:
                aggregate_data['lr_sentiment_p'] = round((aggregate_data['lr_sentiment_p'] / aggregate_data['lr_responses_with_themes'] * 100), 2)
            if aggregate_data['lr_sentiment_n'] > 0:
                aggregate_data['lr_sentiment_n'] = round((aggregate_data['lr_sentiment_n'] / aggregate_data['lr_responses_with_themes'] * 100), 2)
            if aggregate_data['lr_sentiment_m'] > 0:
                aggregate_data['lr_sentiment_m'] = round((aggregate_data['lr_sentiment_m'] / aggregate_data['lr_responses_with_themes'] * 100), 2)

            if aggregate_data['lr_feedback_relational'] > 0:
                aggregate_data['lr_feedback_relational'] = round((aggregate_data['lr_feedback_relational'] / aggregate_data['lr_total_feedback'] * 100), 2)
            if aggregate_data['lr_feedback_transactional'] > 0:
                aggregate_data['lr_feedback_transactional'] = round((aggregate_data['lr_feedback_transactional'] / aggregate_data['lr_total_feedback'] * 100), 2)
            if aggregate_data['lr_feedback_general'] > 0:
                aggregate_data['lr_feedback_general'] = round((aggregate_data['lr_feedback_general'] / aggregate_data['lr_total_feedback'] * 100), 2)

        # sort the data by rating_avg
        sorted_data = sorted(grouped_data.values(), key=lambda x: x['rating_avg'], reverse=True)
        return sorted_data


    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        groupings = {
            'account': [], 
            'agency_brand':[], 
            'client': [], 
            'contact_division': [], 
            'holding_group': [], 
            'market_and_team': [],
            'market_country': [],
            'network': [], 
            # 'office': [],
            'sub_network': [], 
            'team': [],
        }
        if not data:
            return groupings
        
        for key in groupings.keys():
            groupings[key] = self.calc_aggregate_metrics(data, key, round_index, rounds)

        return groupings
    

    def format(self, data: dict):
        formatted_groupings = {}
        for dimension, data in data.items():
            formatted_data = [{
                'id': x['id'],
                'name': x['name'],
                'responses': x['responses'],
                'rating_avg': round(x['rating_avg'], 2),
                'responses_by_primary': x['responses_by_primary'],
                'rating_avg_by_primary': round(x['rating_avg_by_primary'], 2),
                'responses_by_critical': x['responses_by_critical'],
                'rating_avg_by_critical': round(x['rating_avg_by_critical'], 2),
                'movement': self.__rating_change(x['rating_avg'], x['lr_rating_avg']),
                'response_rate': f'{self.__calculate_response_rate(x['responses'], x['total_surveyed'])}%',
                'response_rate_by_primary': f'{self.__calculate_response_rate(x['responses_by_primary'], x['total_surveyed_by_primary'])}%',
                'response_rate_by_critical': f'{self.__calculate_response_rate(x['responses_by_critical'], x['total_surveyed_by_critical'])}%',
                'total_surveyed': x['total_surveyed'],
                'total_surveyed_by_primary': x['total_surveyed_by_primary'],
                'total_surveyed_by_critical': x['total_surveyed_by_critical'],
                'sentiment_positive': x['sentiment_p'],
                'sentiment_negative': x['sentiment_n'],
                'sentiment_mentioned': x['sentiment_m'],
                'movement_sentiment_positive': self.__calculate_point_difference(x['sentiment_p'], x['lr_sentiment_p']),
                'movement_sentiment_negative': self.__calculate_point_difference(x['sentiment_n'], x['lr_sentiment_n']),
                'movement_sentiment_mixed': self.__calculate_point_difference(x['sentiment_m'], x['lr_sentiment_m']),
                'feedback_relational': x['feedback_relational'],
                'feedback_transactional': x['feedback_transactional'],
                'feedback_general': x['feedback_general'],
                'movement_feedback_relational': self.__calculate_point_difference(x['feedback_relational'], x['lr_feedback_relational']),
                'movement_feedback_transactional': self.__calculate_point_difference(x['feedback_transactional'], x['lr_feedback_transactional']),
                'movement_feedback_general': self.__calculate_point_difference(x['feedback_general'], x['lr_feedback_general']),
            } for x in data]
            # ignore any groupings that only have an entry for 'unknown'
            if len(formatted_data) == 1 and formatted_data[0]['id'] == 'unknown':
                continue
            formatted_groupings[dimension] = formatted_data
        return formatted_groupings
