import datetime
from api.clientarea.src.utils.cards.Card import Card


class NoResponses(Card):

    def get_data(self, data:list, round_index:str, rounds:dict = {}, filters:dict = {}, norm:dict = {}):
        responses = []
        count = 0

        for record in data:
            if record['responded']:
                continue

            count += 1
            key = f'nonresponse_{count}'
            survey_link = f'https://survey.thereferralrating.com/survey/{record["survey_id"]}' #FIXME: make this env aware
            responses.append({
                'key': key,
                'panel_member_id': record['Id'],  
                'name': record['contact_name'],
                'contact_type': record['contact_type'],
                'account': record['account_name'],
                'market': record['market_name'],
                'agency': record['client_name'],
                'team': record['team_name'],
                'survey_link': survey_link,
                'survey_name': record['survey_name'],
                'contact_id': record['contact_id'],
                'contact_email': record['contact_email'],
                'followup_action_user': record.get('followup_action_user', ''),
                'followup_action_date': record.get('followup_action_date', ''),
            })
        
        return responses