import re
import unicodedata
from collections import defaultdict
from typing import Any, Dict, List, Tuple
from api.clientarea.src.utils.cards.Card import Card


class ExportResponse(Card):

    def __clean_and_normalise_question_text(self, 
                                            question_text: str, 
                                            survey_name: str, 
                                            agency_office_name: str, 
                                            agency_market_name: str) -> str:
        if not question_text:
            return question_text

        remove_strings:List[str|None] = [survey_name, agency_office_name, agency_market_name]
        for rs in remove_strings:
            if rs and rs in question_text:
                question_text:str = question_text.replace(rs, '')

        question_text:str = question_text.replace('\n', '').replace('\r', '')
        question_text:str = question_text.strip()
        question_text:str = question_text.lower()
        question_text:str = re.sub(r'\s+', ' ', question_text)

        normalized:str = unicodedata.normalize('NFKD', question_text)
        question_text:str = ''.join(c for c in normalized if not unicodedata.combining(c))
        question_text:str = question_text.encode('ascii', 'ignore').decode('utf-8')

        return question_text

    def reformat_data(self, data: list) -> List[Dict[str, Any]]:
        reformatted_data:List = list()
        grouped_extra_questions:defaultdict = defaultdict(list)
        question_number:int = 1

        for item in data:
            reformatted_item:Dict = {}
            for key, value in item.items():
                if isinstance(value, str):
                    clean_val:str = value
                    clean_val:str = clean_val.replace('\n', '').replace('\r', '')
                    clean_val:str = f'"{clean_val.replace('"', '""')}"'
                    reformatted_item[key] = clean_val
                else:
                    reformatted_item[key] = value

            if item.get('is_extra_question', False):
                score_question:str|None = item.get('rating_question_en') or item.get('rating_question')
                feedback_question:str|None = item.get('feedback_question_en') or item.get('feedback_question')

                # strip any agnecy identifiers from the question text so we can use it as a key and group questions
                score_question_cleaned:str|None = self.__clean_and_normalise_question_text(
                    question_text=score_question,
                    survey_name=item.get('survey_name', ''),
                    agency_office_name=item.get('office_name', ''),
                    agency_market_name=item.get('with_market_name', '')
                )
                feedback_question_cleaned:str|None = self.__clean_and_normalise_question_text(
                    question_text=feedback_question,
                    survey_name=item.get('survey_name', ''),
                    agency_office_name=item.get('office_name', ''),
                    agency_market_name=item.get('with_market_name', '')
                )
                reformatted_item['rating_question_cleaned'] = score_question_cleaned
                reformatted_item['feedback_question_cleaned'] = feedback_question_cleaned

                # create a unique key for the extra question based on the cleaned question text
                group_key:Tuple = (score_question_cleaned or "", feedback_question_cleaned or "")

                grouped_extra_questions[group_key].append(reformatted_item)
            else:
                reformatted_data.append(reformatted_item)

        for eq_idx, (eq_key, eq_items) in enumerate(grouped_extra_questions.items()):
            question_number:int = eq_idx + question_number
            was_score_and_feedback:bool = False
            for item in eq_items:
                for original_response in reformatted_data:
                    if (original_response['contact_id'] == item['contact_id'] and original_response['customer_survey_id'] == item['customer_survey_id']):
                        score:int|None = item.get('rating', None)
                        score_question:str|None = item['rating_question_en'] if item.get('rating_question_en', None) else item['rating_question']
                        feedback:str|None = item.get('feedback', None)
                        feedback_question:str|None = item['feedback_question_en'] if item.get('feedback_question_en', None) else item['feedback_question']

                        if score:
                            original_response[f'extra_question_{question_number}_answer'] = score
                            original_response[f'extra_question_{question_number}_question'] = score_question

                            # extra questions can include score and feedback in the same response
                            # however, score comes first so we only need the double check once
                            if feedback:
                                was_score_and_feedback:bool = True
                                original_response[f'extra_question_{question_number+1}_answer'] = feedback
                                original_response[f'extra_question_{question_number+1}_question'] = feedback_question
                        elif feedback:
                            # this is when feedback but no score
                            original_response[f'extra_question_{question_number}_answer'] = feedback
                            original_response[f'extra_question_{question_number}_question'] = feedback_question
                        else:
                            original_response[f'extra_question_{question_number}_answer'] = None
                            original_response[f'extra_question_{question_number}_question'] = None
                
            if was_score_and_feedback:
                question_number += 1

        return reformatted_data


    def get_data(self, data: list, round_index: str, rounds: dict = {}, filters: dict = {}, norm: dict = {}) -> List[Dict[str, Any]]:
        export_data:List = []
        if 'non_responders' in filters:
            export_data:List = [item for item in data if item['responded'] == False]
        else:
            export_data:List = [item for item in data if item['responded'] == True]

        return self.reformat_data(export_data)
