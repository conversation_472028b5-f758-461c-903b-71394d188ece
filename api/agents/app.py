from litestar import Litestar
from litestar.openapi import OpenAPIConfig
from litestar.openapi.plugins import ScalarRenderPlugin
from litestar.middleware.base import DefineMiddleware
from litestar.logging.config import LoggingConfig
from mangum import Mangum
from api.agents.src.routes import routes
from api.agents.src.middleware.salesforce import SalesforceAuthenticationMiddleware


app = Litestar(
    route_handlers=routes,
    logging_config=LoggingConfig(log_exceptions='always'),
    openapi_config=OpenAPIConfig(
        title='Agents API',
        #FIXME: move version to not be hardcoded
        version='0.0.1',
        render_plugins=[ScalarRenderPlugin()],
    ),
)

lambda_handler = Mangum(app)