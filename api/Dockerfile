FROM public.ecr.aws/lambda/python:3.12

# Copy requirements.txt
COPY requirements.txt ${LAMBDA_TASK_ROOT}

# Install the specified packages
RUN pip install -r requirements.txt

# Copy function code
COPY __init__.py ${LAMBDA_TASK_ROOT}
COPY documentdb-global-bundle.pem ${LAMBDA_TASK_ROOT}
COPY lib/*.py ${LAMBDA_TASK_ROOT}/lib/
COPY api/ ${LAMBDA_TASK_ROOT}/api/

# Set the CMD to your handler (could also be done as a parameter override outside of the Dockerfile)
CMD [ "override.me.on.deployment" ]
